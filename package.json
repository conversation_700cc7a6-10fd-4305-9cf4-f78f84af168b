{"name": "video-rendering-api", "version": "0.1.0", "description": "Vidfast Video Rendering API", "main": "app.ts", "scripts": {"build": "npx tsc && copyfiles -u 1 \"src/**/!(*.ts|*.tsx|*.js|*.jsx)\" dist/", "start": "node --max-old-space-size=4096 ./dist/app.js", "dev": "nodemon", "test": "echo \"Error: no test specified\" && exit 1", "deploy-remotion-dev": "remotion lambda sites create src/remotion/index.ts --site-name=boostcast-video-dev --region=eu-west-1"}, "repository": {"type": "git", "url": "git+ssh://**************/vidfast/video-rendering-api.git"}, "author": "vidfast.ai", "license": "ISC", "bugs": {"url": "https://gitlab.com/vidfast/video-rendering-api/issues"}, "homepage": "https://gitlab.com/vidfast/video-rendering-api#readme", "dependencies": {"@remotion/bundler": "4.0.301", "@remotion/cli": "4.0.301", "@remotion/fonts": "4.0.301", "@remotion/lambda": "^4.0.301", "@remotion/renderer": "4.0.301", "amqplib": "^0.10.8", "axios": "^1.6.5", "dotenv": "^16.4.5", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.2", "pino": "^8.19.0", "react-draggable": "^4.4.5", "remotion": "4.0.301"}, "devDependencies": {"@types/amqplib": "^0.10.7", "@types/ffmpeg-static": "^3.0.3", "@types/fluent-ffmpeg": "^2.1.24", "@types/node": "^20.10.5", "@types/react": "^18.3.2", "copyfiles": "^2.4.1", "nodemon": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}