stages:
  - build
  - deploy

build:
  image: docker:20.10.16
  stage: build
  only:
    - main
  services:
    - docker:20.10.16-dind
  script:
    - docker buildx create --use
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker buildx build --platform linux/amd64,linux/arm64 -t $CI_REGISTRY_IMAGE:latest -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA --push .

deploy-remotion:
  image: node:18-alpine
  stage: deploy
  only:
    refs:
      - main
    changes:
      - src/remotion/**/*
  script:
    - npm ci
    - npx remotion lambda sites create src/remotion/index.ts --site-name=boostcast-video --region=eu-west-1