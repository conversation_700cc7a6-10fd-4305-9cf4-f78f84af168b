import * as dotenv from "dotenv";
import path from "path";
import * as fs from "fs";

const SECRETS_PATH = "/run/secrets";

function read_secret(name: string): string {
  const filePath = `${SECRETS_PATH}/${name}`;
  if (fs.existsSync(filePath)) {
    return fs.readFileSync(filePath, "utf8").trim();
  }
}

function load_from_secret_or_env(name: string): string {
  const upperName = name.toUpperCase();
  const lowerName = name.toLowerCase();
  let value =
    read_secret(upperName) ||
    read_secret(lowerName) ||
    process.env[upperName] ||
    process.env[lowerName];
  if (value) return value;
  throw Error(`Cannot load "${name}" from env or secrets`);
}

function set_secret_as_env(name: string): void {
  process.env[name] = load_from_secret_or_env(name)
}

dotenv.config({ path: path.resolve(__dirname, "../.env") });

interface RabbitMQConfig {
  url: string;
  messageChannels: {
    renderRequestQueue: string;
    renderResultQueue: string;
  };
}

interface LambdaConfig {
  accessKeyId: string;
  secretAccessKey: string;
  region: string;
}

interface Config {
  rabbitMQ: RabbitMQConfig;
  lambda: LambdaConfig;
}

export const config: Config = {
  rabbitMQ: {
    url: load_from_secret_or_env("RABBITMQ_URL"),
    messageChannels: {
      renderRequestQueue: "clip.render.request",
      renderResultQueue: "clip.render.response",
    },
  },
  lambda: {
    accessKeyId: load_from_secret_or_env("REMOTION_AWS_ACCESS_KEY_ID"),
    secretAccessKey: load_from_secret_or_env("REMOTION_AWS_SECRET_ACCESS_KEY"),
    region: load_from_secret_or_env("AWS_REGION"),
  },
};
// Remotion lambda needs those as env vars
set_secret_as_env("REMOTION_AWS_ACCESS_KEY_ID")
set_secret_as_env("REMOTION_AWS_SECRET_ACCESS_KEY")
set_secret_as_env("AWS_REGION")



