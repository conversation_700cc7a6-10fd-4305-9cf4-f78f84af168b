import {
  renderMediaOnLambda,
  getRenderProgress,
  getFunctions,
  getSites,
  AwsRegion,
} from "@remotion/lambda/client";
import { config } from "./config";
import pino from "pino";
import {
  deploySite,
  getOrCreateBucket,
  deployFunction,
} from "@remotion/lambda";
import path from "path";

const logger = pino({ name: "lambda-renderer" });

export const renderClipOnLambda = async (renderConfig) => {
  try {
    logger.info("Starting Lambda render...");

    const region = config.lambda.region as AwsRegion;

    // Get the Lambda function
    let functions = await getFunctions({
      region: region,
      compatibleOnly: true,
    });

    let functionName: string;

    if (functions.length === 0) {
      logger.error(
        "No Lambda functions found!"
      );
      return;
    } else {
      functionName = functions[0].functionName;
    }

    // Get or deploy site
    let sites = await getSites({
      region: region,
    });

    let siteUrl: string;

    if (sites.sites.length === 0) {
      logger.info("No sites found. Deploying site automatically...");
      const { bucketName } = await getOrCreateBucket({
        region: "eu-west-1",
      });

      const entryPoint = path.resolve(
        process.env.NODE_ENV === "production"
          ? "./dist/remotion/index.js"
          : "./src/remotion/index.ts"
      );
      const deployResult = await deploySite({
        region: region,
        entryPoint: entryPoint,
        siteName: "boostcast-video",
        bucketName: bucketName, // Add your bucket name from config or set it directly
      });

      siteUrl = deployResult.serveUrl;
      logger.info(`Site deployed successfully: ${siteUrl}`);
    } else {
      siteUrl = sites.sites[0].serveUrl;
      logger.info(`Using existing site: ${siteUrl}`);
    }

    logger.info(`Using function: ${functionName}, site: ${siteUrl}`);

    // Start the render on Lambda
    const { renderId, bucketName } = await renderMediaOnLambda({
      region: region,
      functionName: functionName,
      serveUrl: siteUrl,
      composition: "MyComp",
      inputProps: renderConfig.inputProps,
      codec: "h264",
      privacy: "private", // Make output private for security
      outName: renderConfig.outputLocation.split("/").pop(), // Extract filename
    });

    logger.info(`Render started with ID: ${renderId}`);

    // Poll for completion with exponential backoff
    let pollInterval = 10000; // Start with 10 seconds
    const maxPollInterval = 30000; // Max 30 seconds

    while (true) {
      try {
        const progress = await getRenderProgress({
          renderId,
          bucketName,
          region: region,
          functionName: functionName,
          skipLambdaInvocation: true, // Use direct S3 calls instead of Lambda
        });

        logger.debug(
          `Render progress: ${Math.round(progress.overallProgress * 100)}%`
        );

        if (progress.done) {
          if (progress.outputFile) {
            logger.info(
              `Render completed successfully: ${progress.outputFile}`
            );
            return {
              success: true,
              outputUrl: progress.outputFile,
              renderId,
              bucketName,
            };
          } else {
            throw new Error("Render completed but no output file found");
          }
        }

        if (progress.fatalErrorEncountered) {
          throw new Error(`Render failed: ${JSON.stringify(progress.errors)}`);
        }

        // Wait with current interval
        await new Promise((resolve) => setTimeout(resolve, pollInterval));

        // Increase interval for next iteration (exponential backoff)
        // Validate the Error message
      } catch (error) {
        logger.error(`Error occurred: ${error}`); // @TODO CREATE A CUSTOM ERROR CLASS
        if (
          error.message?.includes("Rate Exceeded") ||
          error.message?.includes("TooManyRequests")
        ) {
          logger.warn(`Rate limit hit, backing off to ${pollInterval}ms`);
          await new Promise((resolve) => setTimeout(resolve, pollInterval));
          pollInterval = Math.min(pollInterval * 1.2, maxPollInterval);
          continue;
        }
        throw error; // Re-throw non-rate-limit errors
      }
    }
  } catch (error) {
    logger.error(`Lambda render failed: ${error.message}`);
    throw error;
  }
};
