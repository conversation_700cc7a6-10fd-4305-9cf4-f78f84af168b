import { bundle } from "@remotion/bundler";
import { renderMedia, selectComposition } from "@remotion/renderer";
import path from "path";

export const renderClip = async (config) => {
  const entryPoint =
    process.env.NODE_ENV === "production"
      ? path.resolve("./dist/remotion/index.js")
      : path.resolve("./src/remotion/index.ts");

  const bundleLocation = await bundle({
    entryPoint: entryPoint,
  });
  // Get the composition you want to render. Pass `inputProps` if you
  // want to customize the duration or other metadata.
  const composition = await selectComposition({
    serveUrl: bundleLocation,
    id: "MyComp",
    inputProps: config.inputProps,
  });

  // console.warn("config.inputProps", config.inputProps.props.sourceVideo.media_metadata);
  // console.warn("config.inputProps 2", config.inputProps.props.clipMetadata);

  // Render the video. Pass the same `inputProps` again
  // if your video is parametrized with data.
  await renderMedia({
    composition,
    serveUrl: bundleLocation,
    codec: "h264",
    outputLocation: `${path.resolve(config.outputLocation)}`,
    inputProps: config.inputProps,
    forSeamlessAacConcatenation: true,
  });
};
