import { ISubtitles, IVideo } from "./remotion/types";

export interface CropBox {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}

export interface Segment {
  start: number;
  end: number;
  crop_box?: CropBox;
}

export interface Word {
  word: string;
  start: number;
  end: number;
}

export interface Subtitle {
  start: number;
  end: number;
  text: string;
  words: Word[];
}
export interface SubtitlePosition {
  x: number;
  y: number;
}

export interface SubtitlesConfig {
  fontSize: number;
  fontFamily: string;
  subColor: string;
  subBackground: string;
  highlightBgColor: string;
  position: SubtitlePosition;
}

export interface RenderRequestBody {
  ownerId: string;
  clipId: string;
  inputUrl: string;
  outputUrl: string;
  segments: Segment[];
  subtitles?: ISubtitles;
  video: IVideo;
  font: {
    id: string;
    created_at: string;
    updated_at: string;
    family: string;
    sub_family: string;
    file_path: string;
    owner_id: string;
  };
}

export enum RenderStatus {
  PROCESSING = "PROCESSING",
  SUCCESS = "SUCCESS",
  ERROR = "ERROR",
}
export interface RenderResult {
  ownerId: string;
  status: RenderStatus;
  error?: string;
  clipId: string;
}
