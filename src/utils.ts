import { staticFile } from "remotion";
import { RenderRequestBody, Segment, Subtitle } from "./models";
import { ISubtitle } from "./remotion/types";

/**
 * Adjusts subtitle timings to match new video segments.
 *
 * @param subtitles - Original subtitles
 * @param segments - New video segments
 * @returns New array of subtitles with adjusted timings
 *
 * Notes:
 * - Keeps subtitles that fit in the new segments
 * - Adjusts subtitle and word timings to match new video
 * - [!]Assumes segments are in order and don't overlap
 *
 * Example:
 * Original subtitle: 61 to 71 seconds
 * Segment: 60 to 70 seconds
 * Result: 1 to 10 seconds in new video
 */
export function calibrateSubtitles(
  subtitles: Subtitle[],
  segments: Segment[]
): Subtitle[] {
  if (segments.length === 0) return subtitles;

  const calibratedSubtitles: Subtitle[] = [];
  let newVideoTime = 0;
  let subtitleIndex = 0;

  for (const segment of segments) {
    const segmentDuration = segment.end - segment.start;

    while (
      subtitleIndex < subtitles.length &&
      subtitles[subtitleIndex].start < segment.end
    ) {
      const subtitle = subtitles[subtitleIndex];

      if (subtitle.end <= segment.start) {
        subtitleIndex++;
        continue;
      }

      const calibratedSubtitle: Subtitle = {
        start: Math.max(
          newVideoTime,
          newVideoTime + (subtitle.start - segment.start)
        ),
        end: Math.min(
          newVideoTime + segmentDuration,
          newVideoTime + (subtitle.end - segment.start)
        ),
        text: subtitle.text,
        words: subtitle.words.map((word) => ({
          ...word,
          start: Math.max(
            newVideoTime,
            newVideoTime + (word.start - segment.start)
          ),
          end: Math.min(
            newVideoTime + segmentDuration,
            newVideoTime + (word.end - segment.start)
          ),
        })),
      };

      calibratedSubtitles.push(calibratedSubtitle);
      subtitleIndex++;
    }

    newVideoTime += segmentDuration;
  }

  return calibratedSubtitles;
}

type MemoizedState = {
  lastIndex: number;
  subtitles: ISubtitle[];
};

// Helper function to check if a time is within a subtitle's range
const isTimeInSubtitle = (time: number, subtitle: ISubtitle): boolean => {
  return time >= subtitle.start && time < subtitle.end;
};

// Binary search function
const binarySearchSubtitle = (
  subtitles: ISubtitle[],
  currentTime: number
): number => {
  let left = 0;
  let right = subtitles.length - 1;

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    const subtitle = subtitles[mid];

    if (isTimeInSubtitle(currentTime, subtitle)) {
      return mid;
    }

    if (currentTime < subtitle.start) {
      right = mid - 1;
    } else {
      left = mid + 1;
    }
  }

  return -1; // Not found
};

// Main search function with memoization
export const findSubtitle = (() => {
  let memoizedState: MemoizedState | null = null;

  return (subtitles: ISubtitle[], currentTime: number): ISubtitle | null => {
    // Initialize or update memoized state if subtitles array has changed
    if (!memoizedState || memoizedState.subtitles !== subtitles) {
      memoizedState = { lastIndex: 0, subtitles };
    }

    const { lastIndex } = memoizedState;

    // Check if the last found subtitle is still valid
    if (isTimeInSubtitle(currentTime, subtitles[lastIndex])) {
      return subtitles[lastIndex];
    }

    // Perform binary search
    const foundIndex = binarySearchSubtitle(subtitles, currentTime);

    if (foundIndex !== -1) {
      memoizedState.lastIndex = foundIndex;
      return subtitles[foundIndex];
    }

    return null; // No subtitle found for the current time
  };
})();

import { ProcessedSegment } from "./remotion/types";

export const secondsToFrames = (seconds: number, fps: number): number => {
  return Math.floor(seconds * fps);
};

export const calculateSegmentDuration = (segment: ProcessedSegment): number => {
  return segment.endFrame - segment.startFrame;
};

export const getTotalDuration = (segments: ProcessedSegment[]): number => {
  return segments.reduce(
    (acc, segment) => acc + calculateSegmentDuration(segment),
    0
  );
};

export const makeEven = (num: number): number => {
  return Math.floor(num / 2) * 2;
};

export const calculateCompositionDimensions = (
  segments: Segment[],
  originalVideoDimensions: { width: number; height: number }
) => {
  const segmentWithCrop = segments.find((s) => s.crop_box);

  if (!segmentWithCrop?.crop_box) {
    // If no crop box, use original video dimensions
    return {
      width: makeEven(originalVideoDimensions.width),
      height: makeEven(originalVideoDimensions.height),
    };
  }

  // If crop box exists, use its dimensions
  const { crop_box } = segmentWithCrop;

  return {
    width: makeEven(Math.abs(crop_box.x2 - crop_box.x1)),
    height: makeEven(Math.abs(crop_box.y2 - crop_box.y1)),
  };
};

export const detectAspectRatio = (width: number, height: number): string => {
  const ratio = width / height;

  if (Math.abs(ratio - 16 / 9) < 0.1) return "16:9";
  if (Math.abs(ratio - 9 / 16) < 0.1) return "9:16";
  if (Math.abs(ratio - 1) < 0.1) return "1:1";

  // Default to the closest standard ratio if none of the above match
  return ratio > 1 ? "16:9" : "9:16";
};

export const getFrameDimensions = (
  ratio: string,
  videoPlayerDimensions: { width: number; height: number }
): { frameWidth: number; frameHeight: number } => {
  if (ratio === "original") {
    return {
      frameWidth: videoPlayerDimensions.width,
      frameHeight: videoPlayerDimensions.height,
    };
  }

  let frameWidth, frameHeight;
  const containerWidth = videoPlayerDimensions.width;
  const containerHeight = videoPlayerDimensions.height;

  // Calculate both dimensions based on container constraints and pick the one that fits
  switch (ratio) {
    case "16:9":
      // Calculate dimensions both ways
      const w1 = containerWidth;
      const h1 = w1 * (9 / 16);

      const h2 = containerHeight;
      const w2 = h2 * (16 / 9);

      // Choose the dimensions that fit within the container
      if (h1 <= containerHeight) {
        frameWidth = w1;
        frameHeight = h1;
      } else {
        frameWidth = w2;
        frameHeight = h2;
      }
      break;

    case "9:16":
      // Calculate dimensions both ways
      const w9 = containerWidth;
      const h9 = w9 * (16 / 9);

      const h16 = containerHeight;
      const w16 = h16 * (9 / 16);

      // Choose the dimensions that fit within the container
      if (h9 <= containerHeight) {
        frameWidth = w9;
        frameHeight = h9;
      } else {
        frameWidth = w16;
        frameHeight = h16;
      }
      break;

    case "1:1":
      // Use the smaller dimension for square
      const size = Math.min(containerWidth, containerHeight);
      frameWidth = size;
      frameHeight = size;
      break;

    default:
      throw new Error(`Unsupported aspect ratio: ${ratio}`);
  }

  return { frameWidth, frameHeight };
};

/**
 * Gets the CSS font family name based on family and sub-family
 * This is needed to correctly reference the loaded fonts in CSS
 * @param family The font family name
 * @param subFamily The sub-family name
 * @returns The CSS font family name
 */
export const getCssFontFamily = (family: string, subFamily: string): string => {
  return `${family}-${subFamily}`;
};

/**
 * Loads a font using the FontFace API
 * @param family The font family name
 * @param fontPath The path to the font file
 * @param subFamily The sub-family name (e.g., "Regular", "Bold")
 * @returns A promise that resolves when the font is loaded
 */
export const loadFont = async (
  family: string,
  fontPath: string,
  subFamily: string
): Promise<FontFace> => {
  // Create a unique font family name that includes both family and subFamily
  // This ensures each variant is properly loaded and distinguishable
  const fontName = `${family}-${subFamily}`;

  try {
    // Clean and normalize the font URL
    let fontUrl = staticFile(fontPath);

    console.log(`Loading font ${fontName} from URL: ${fontUrl}`);

    // Create a new FontFace instance
    const fontFace = new FontFace(fontName, `url(${fontUrl})`);

    // Add to the document.fonts (FontFaceSet)
    document.fonts.add(fontFace);

    // Load the font
    await fontFace.load();

    console.log(`Font loaded successfully: ${fontName}`);
    return fontFace;
  } catch (error) {
    console.error(`Failed to load font: ${family}-${subFamily}`, error);

    // Log more details about the error to help with debugging
    if (error instanceof Error) {
      console.error(`Error message: ${error.message}`);
      console.error(`Error stack: ${error.stack}`);
    }

    if (error instanceof TypeError && error.message.includes("NetworkError")) {
      console.error("This appears to be a network error. Check the following:");
      console.error("1. Is the font URL accessible?");
      console.error("2. Is there a CORS issue with the font server?");
      console.error("3. Is the API returning the correct font paths?");
    }

    throw error;
  }
};

/**
 * Cleans and normalizes a font URL to handle various edge cases
 * @param url The original font URL from the API
 * @returns A cleaned and normalized URL
 */
export const cleanFontUrl = (url: string): string => {
  // If it's a data URL, return it as is
  if (url.startsWith("data:")) {
    return url;
  }

  // Handle recursive URL encoding and repeated domains
  if (url.includes("vidfast-fonts.vidfast.ai")) {
    // First check for over-nested URLs (multiple domains)
    const domainCount = (url.match(/vidfast-fonts\.vidfast\.ai/g) || []).length;

    if (domainCount > 1) {
      // Extract essential parts: the actual S3 path with user ID and file ID plus query params
      const pathMatch = url.match(
        /dev\/([a-f0-9-]+\/[a-f0-9-]+\.ttf(?:\?[^#\s]+)?)/i
      );

      if (pathMatch && pathMatch[1]) {
        return `https://vidfast-fonts.vidfast.ai/${pathMatch[0]}`;
      }

      // Another attempt to extract the path
      const uuidMatch = url.match(
        /([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}\/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}\.ttf)/i
      );

      if (uuidMatch && uuidMatch[1]) {
        // Find the query parameters if they exist
        const queryParams = url.match(
          /(\?X-Amz-Algorithm=AWS4-HMAC-SHA256[^#\s]+)/i
        );
        const queryString = queryParams ? queryParams[1] : "";

        return `https://vidfast-fonts.vidfast.ai/dev/${uuidMatch[1]}${queryString}`;
      }
    }

    // Check for URL encoded segments
    if (url.includes("%")) {
      try {
        // Try to decode once
        const decodedUrl = decodeURIComponent(url);

        // If the decoded URL is different and still has encoded parts,
        // recursively clean it
        if (decodedUrl !== url && decodedUrl.includes("%")) {
          return cleanFontUrl(decodedUrl);
        }

        // If decoding worked but no more encoding, use this URL
        if (decodedUrl !== url) {
          return decodedUrl;
        }
      } catch (e) {
        // If decoding fails, continue with original url
        console.warn("Failed to decode URL:", url);
      }
    }

    // If the URL has https prefixed with http, fix it
    if (url.match(/^http:\/\/https:\/\//)) {
      return url.replace(/^http:\/\/https:\/\//, "https://");
    }

    // If we reached here and the URL still seems valid, return it
    if (url.match(/^https?:\/\//)) {
      return url;
    }
  }

  // If none of the special cases apply, return the original URL
  return url;
};

/**
 * Strips segments from a render request for more readable logging
 * @param renderRequest
 */
export function stripRenderRequest(
  renderRequest: RenderRequestBody
): Partial<RenderRequestBody> {
  return {
    ownerId: renderRequest.ownerId,
    clipId: renderRequest.clipId,
    inputUrl: renderRequest.inputUrl,
    outputUrl: renderRequest.outputUrl,
    font: renderRequest.font,
    video: renderRequest.video,
  };
}
