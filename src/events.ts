import amqplib from "amqplib";
import pino from "pino";

const logger = pino({ name: "events-manager" });

class EventsManager {
  private connection: any;
  private workers: any[];
  constructor() {
    this.connection = null;
    this.workers = [];
  }

  async connect(rabbitmqUrl) {
    try {
      this.connection = await amqplib.connect(rabbitmqUrl);
      logger.info(`Connected to RabbitMQ url: ${rabbitmqUrl}`);
    } catch (e) {
      logger.error(e.toString());
      process.exit(1); // Exit on connection failure
    }
  }

  registerWorker(worker) {
    this.workers.push(worker);
  }

  startWorkers() {
    this.workers.forEach((worker) => worker()); // Start all workers
  }

  async publish(queueName, message) {
    const channel = await this.connection.createChannel();
    await channel.assertQueue(queueName, { durable: true });

    channel.sendToQueue(queueName, Buffer.from(JSON.stringify(message)));
  }

  async notify(exchangeName, message) {
    const channel = await this.connection.createChannel();
    await channel.assertExchange(exchangeName, "fanout", { durable: true });

    channel.publish(exchangeName, "", Buffer.from(JSON.stringify(message)));
  }

  async processMessage(channel, dataHandler, msg) {
    if (msg) {
      try {
        const data = JSON.parse(msg.content.toString());
        await dataHandler(data); // TODO:  Handle data and potential validation here
        channel.ack(msg);
      } catch (error) {
        logger.error(`Error processing message: ${msg}`, error);
      }
    }
  }
  queueWorker(queueName, dataHandler) {
    // 'model' concept removed - use your data validation within dataHandler
    const worker = async () => {
      logger.info(`Starting ${queueName} worker`);
      const channel = await this.connection.createChannel();
      await channel.assertQueue(queueName, { durable: true });

      channel.consume(queueName, async (msg) => {
        await this.processMessage(channel, dataHandler, msg);
      });
    };
    return worker;
  }

  notificationWorker(exchangeName, dataHandler) {
    const worker = async () => {
      logger.info(`Starting ${exchangeName} worker`);
      const channel = await this.connection.createChannel();
      await channel.assertExchange(exchangeName, "fanout", { durable: true });

      const queueName = await channel.assertQueue("", { exclusive: true });
      await channel.bindQueue(queueName, exchangeName, "");

      channel.consume(queueName, async (msg) => {
        await this.processMessage(channel, dataHandler, msg);
      });
    };
    return worker;
  }
}

export const eventsManager = new EventsManager();
