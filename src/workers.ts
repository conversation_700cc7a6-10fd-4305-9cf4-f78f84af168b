import { RenderRequestBody, RenderResult, RenderStatus } from "./models";
import {
  downloadFile,
  getUrlFileExtension,
  uploadFile,
  getFilename,
  getRandomFilePath,
  safeDeleteFile
} from "./fileUtils";
import { eventsManager } from "./events";
import { renderClipOnLambda } from "./lambdaRenderer";
import { calculateCompositionDimensions, calibrateSubtitles, stripRenderRequest } from "./utils";
import pino from "pino";
import { getVideoMetadata } from "@remotion/renderer";
import path from "path";
import { AwsRegion, downloadMedia } from "@remotion/lambda";
import { config } from "./config";

const logger = pino();

async function videoRenderHandler(renderRequest: RenderRequestBody) {
  //remove segments from the request
  const strippedRequest = stripRenderRequest(renderRequest);
  logger.info(
    "Received render request:",
    JSON.stringify(strippedRequest, null, 2)
  );


  const result: RenderResult = {
    ownerId: renderRequest.ownerId,
    clipId: renderRequest.clipId,
    status: RenderStatus.PROCESSING, // Its pointless status as no one will consume it
  };

  const tempFiles: string[] = [];

  try {
    // Download input file temporarily to get video metadata
    logger.info(
      `Downloading input file for metadata extraction for clipId: ${JSON.stringify(
        renderRequest.clipId
      )}`
    );
    const sourceVideoInputPath = `public/${getRandomFilePath(
      getUrlFileExtension(renderRequest.inputUrl)
    )}`; // Added public/ prefix
    tempFiles.push(sourceVideoInputPath);
    await downloadFile(renderRequest.inputUrl, sourceVideoInputPath);

    // Note: No need to download font since we're using S3 URL directly

    // Render clip
    logger.info(
      `Rendering video for clipId: ${JSON.stringify(renderRequest.clipId)}`
    );
    const outputPath = getRandomFilePath(
      getUrlFileExtension(renderRequest.outputUrl)
    );
    tempFiles.push(outputPath);

    const absInputPath = path.resolve(sourceVideoInputPath);
    const clipMetadata = await getVideoMetadata(absInputPath);

    const compositionDimensions = calculateCompositionDimensions(
      renderRequest.segments,
      {
        width: clipMetadata.width,
        height: clipMetadata.height,
      }
    );

    // Calibrate subtitles if they exist
    if (
      renderRequest.subtitles &&
      Object.keys(renderRequest.subtitles).length
    ) {
      renderRequest.subtitles.items = calibrateSubtitles(
        renderRequest.subtitles.items,
        renderRequest.segments
      );
    }

    // Render clip using Lambda
    logger.info(
      `Rendering video using Lambda for clipId: ${JSON.stringify(
        renderRequest.clipId
      )}`
    );

    const lambdaResult = await renderClipOnLambda({
      outputLocation: outputPath,
      inputProps: {
        props: {
          videoSrc: renderRequest.inputUrl,
          font: renderRequest.font
            ? {
                family: renderRequest.font.family,
                subFamily: renderRequest.font.sub_family,
                filePath: renderRequest.font.file_path, // Use S3 URL directly
              }
            : null,
          sourceVideo: renderRequest.video,
          clipMetadata: clipMetadata,
          compositionDimensions,
          subtitles: renderRequest.subtitles,
          segments: renderRequest.segments,
          scale: 1,
          renderMode: true,
          configUpdate: () => {},
        },
      },
    });

    // The Lambda render returns an S3 URL, but we still need to upload to the expected location
    // Download from S3 and upload to the final destination

    if (lambdaResult.success && lambdaResult.outputUrl) {
      logger.info(`Downloading from Lambda S3: ${lambdaResult.outputUrl}`);
      try {
        // Download the rendered video from Lambda S3
        logger.info(`Downloading video from Lambda S3 to: ${outputPath}`);
        await downloadMedia({
          bucketName: lambdaResult.bucketName,
          region: config.lambda.region as AwsRegion,
          renderId: lambdaResult.renderId,
          outPath: outputPath,
        });
        logger.info(
          `Downloaded video successfully from Lambda S3 to: ${outputPath}`
        );
      } catch (error) {
        logger.error(`Failed to download from Lambda S3: ${error.message}`);
        throw error;
      }
    } else {
      throw new Error("Lambda rendering failed or returned no output URL");
    }

    // Upload file
    logger.info(
      `Uploading video for clipId: ${JSON.stringify(renderRequest.clipId)}`
    );
    try {
      await uploadFile(renderRequest.outputUrl, outputPath);
    } catch (uploadErr) {
      logger.error(`Upload Upload ERR: ${uploadErr}`);
      // Log the error but continue as render succeeded
      result.status = RenderStatus.ERROR;
      result.error = `Upload failed but render succeeded: ${uploadErr.message}`;
    }
  } catch (error) {
    result.error = error.toString();
    result.status = RenderStatus.ERROR;
    logger.error(`Error during video rendering: ${error.stack}`);
  } finally {
    // Clean up temporary files
    await Promise.all(tempFiles.map(safeDeleteFile));

    // Publish result
    try {
      result.status = RenderStatus.SUCCESS;
      logger.info(`Sending result: ${JSON.stringify(result)}`);
      await eventsManager.publish("clip.render.result", result);
    } catch (publishError) {
      logger.error(`Failed to publish result: ${publishError.message}`);
    }
  }
}

export const videoRenderWorker = eventsManager.queueWorker(
  "clip.render.request",
  videoRenderHandler
);

