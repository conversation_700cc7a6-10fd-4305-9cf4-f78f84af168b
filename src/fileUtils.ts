import * as fsp from "fs/promises";
import { randomUUID } from "crypto";
import * as Path from "path";
import axios from "axios";
import * as fs from "fs";
import pino from "pino";
import { request } from "https";

const logger = pino();

export function getRandomFilePath(extension: string) {
  const fileExtension = extension.startsWith(".") ? extension : `.${extension}`;
  return `${randomUUID()}${fileExtension}`;
}

export async function safeDeleteFile(filePath: string) {
  try {
    await fsp.unlink(filePath);
  } catch (error) {
    if (error.code !== "ENOENT") {
      logger.warn(
        `Failed to delete temporary file ${filePath}: ${error.message}`
      );
    }
  }
}

export function getUrlFileExtension(url: string): string {
  return Path.extname(new URL(url).pathname || "");
}

export async function downloadFile(
  url: string,
  downloadPath: string
): Promise<void> {
  const response = await axios.get(url, { responseType: "stream" });

  return await fsp.writeFile(downloadPath, response.data);
}

export function getFilename(fullPath: string) {
  return Path.basename(fullPath);
}

export async function uploadFile(urlStr: string, filePath: string) {
  const buf = await fs.promises.readFile(filePath);
  const u = new URL(urlStr);

  await new Promise<void>((resolve, reject) => {
    const req = request(
      {
        method: "PUT",
        hostname: u.hostname,
        path: u.pathname + u.search,
        // do NOT send Content-Type; only Content-Length
        headers: { "Content-Length": Buffer.byteLength(buf) },
      },
      (res) => {
        if (res.statusCode && res.statusCode >= 200 && res.statusCode < 300)
          return resolve();
        let body = "";
        res.on("data", (c) => (body += c));
        res.on("end", () =>
          reject(new Error(`S3 PUT ${res.statusCode}: ${body}`))
        );
      }
    );
    req.on("error", reject);
    req.write(buf);
    req.end();
  });
}
