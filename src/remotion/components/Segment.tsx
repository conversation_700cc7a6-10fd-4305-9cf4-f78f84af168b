import React, { useState, CSSProperties } from "react";
import { useCurrentFrame, useCurrentScale, useVideoConfig } from "remotion";
import { WordComponent } from "./Word";
import { ISubtitle, ISubtitles, IVideo, IVideoMetadata } from "../types";
import { AnimationType, getAnimationStyles } from "./../helpers";
import { getCssFontFamily } from "../../utils";

interface SegmentProps {
  segment: ISubtitle;
  animationType: AnimationType;
  subtitles: ISubtitles;
  sourceVideoMetadata: IVideoMetadata;
  compositionDimensions: { width: number; height: number };
}

const InnerComponent: React.FC<SegmentProps> = ({
  segment,
  animationType,
  subtitles,
  sourceVideoMetadata,
  compositionDimensions,
}) => {
  const frame = useCurrentFrame();
  const scale = useCurrentScale();
  const { fps } = useVideoConfig();

  // Video ratio for actual video dimensions / video player dimensions
  const videoRatio: number =
    compositionDimensions.height / sourceVideoMetadata.height;

  const [wordRefs] = useState(() => {
    return new Array(segment.words.length)
      .fill(0)
      .map(() => React.createRef<HTMLSpanElement>());
  });

  // Animation for the entire subtitle segment
  const segmentAnimationStyles = getAnimationStyles(animationType, {
    frame,
    fps,
    startFrame: segment.start * fps,
    endFrame: segment.end * fps,
  });

  // Shadow styles to be applied
  const shadowStyle: CSSProperties = {};

  // Apply shadow if it exists
  if (subtitles.config.shadow) {
    const { color, opacity, offset, blur } = subtitles.config.shadow;
    // Scale the opacity down to avoid overwhelming the text
    const adjustedOpacity = Math.min(opacity, 70);
    const opacityHex = Math.floor((adjustedOpacity / 100) * 255)
      .toString(16)
      .padStart(2, "0");
    const shadowColor = `${color}${opacityHex}`;

    const offsetX = offset ? (offset.x * videoRatio) / scale : 0;
    const offsetY = offset ? (offset.y * videoRatio) / scale : 0;
    const blurRadius = blur ? (blur * videoRatio) / scale : 0;

    // Apply shadow as text-shadow for better quality
    shadowStyle.textShadow = `${offsetX}px ${offsetY}px ${blurRadius}px ${shadowColor}`;
  }

  // Stroke styles
  const strokeStyle: CSSProperties = {};

  const fontSize = (subtitles.config.size * videoRatio) / scale;

  // Apply stroke if it exists
  if (subtitles.config.stroke) {
    const { color, weight, opacity } = subtitles.config.stroke;

    // Scale down the opacity to make text more visible
    const adjustedOpacity = Math.min(opacity, 85);
    const opacityHex = Math.floor((adjustedOpacity / 100) * 255)
      .toString(16)
      .padStart(2, "0");

    const strokeColor = `${color}${opacityHex}`;

    // Use WebkitTextStroke for higher quality stroke rendering
    strokeStyle.WebkitTextStroke = `${weight}px ${strokeColor}`;
    strokeStyle.paintOrder = "stroke fill";

    // Add a fallback for browsers that don't support WebkitTextStroke
    (strokeStyle as any).textStroke = `${weight}px ${strokeColor}`;
  }

  // Get the font CSS name using the utility (family-subfamily)
  const fontFamily = getCssFontFamily(
    subtitles.config.font.family,
    subtitles.config.font.sub_family
  );

  return (
    <div
      style={{
        textAlign: "center",
      }}
    >
      <div
        style={{
          cursor: "pointer",
          fontSize,
          fontFamily: `"${fontFamily}", Arial, sans-serif`,
          fontWeight: "normal", // Since we're using the exact font variant, use normal weight
          color: subtitles.config.color,
          lineHeight: "1.2",
          letterSpacing: "0.02em",
          fontSmooth: "always",
          WebkitFontSmoothing: "antialiased",
          MozOsxFontSmoothing: "grayscale",
          ...strokeStyle,
          ...shadowStyle,
          ...segmentAnimationStyles,
        }}
      >
        {segment.words.map((word, i) => {
          return (
            <>
              <WordComponent
                ref={wordRefs[i]}
                key={i}
                index={i}
                word={word}
                animationType={animationType}
                subtitlesConfig={subtitles.config}
              />
              {i < segment.words.length - 1 && " "}
            </>
          );
        })}
      </div>
    </div>
  );
};

export const SegmentComp: React.FC<SegmentProps> = ({
  segment,
  animationType,
  subtitles,
  sourceVideoMetadata,
  compositionDimensions,
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  const timeInSeconds = frame / fps;
  const { start, end } = segment;

  if (timeInSeconds < start || timeInSeconds > end) {
    return null;
  }

  return (
    <InnerComponent
      key={segment.start}
      segment={segment}
      animationType={animationType}
      subtitles={subtitles}
      sourceVideoMetadata={sourceVideoMetadata}
      compositionDimensions={compositionDimensions}
    />
  );
};
