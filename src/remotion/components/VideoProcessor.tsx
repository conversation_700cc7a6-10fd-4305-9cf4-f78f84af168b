interface InputSegment {
  start: number;
  end: number;
  crop_box?: CropBox;
  transition?: {
    type: "fade" | "push" | "wipe" | "zoom" | "dissolve";
    durationFrames: number;
    options?: any;
  };
}

interface ProcessedSegment extends Omit<InputSegment, "crop_box"> {
  startFrame: number;
  endFrame: number;
  transitionDurationFrames: number;
  cropBox?: CropBox;
}

interface VideoProcessorProps {
  segments: InputSegment[];
  videoSrc: string;
  opacity?: number;
  defaultTransitionDuration?: number;
  fps?: number;
  dimensions: {
    width: number;
    height: number;
  };
}

import React, {
  useRef,
  useCallback,
  useState,
  useEffect,
  useMemo,
} from "react";
import { AbsoluteFill, Video, useCurrentFrame } from "remotion";
import { CanvasDimensions } from "./../types";
import { TransitionEffects } from "./../TransitionEffects";
import { secondsToFrames, makeEven } from "./../../utils";
import { CropBox } from "../../models";

declare global {
  interface VideoFrameMetadata {
    presentationTime: DOMHighResTimeStamp;
    expectedDisplayTime: DOMHighResTimeStamp;
    width: number;
    height: number;
    mediaTime: number;
    presentedFrames: number;
    processingDuration?: number;
    captureTime?: DOMHighResTimeStamp;
    receiveTime?: DOMHighResTimeStamp;
    rtpTimestamp?: number;
  }
  type VideoFrameRequestCallbackId = number;
  interface HTMLVideoElement extends HTMLMediaElement {
    requestVideoFrameCallback(
      callback: (now: DOMHighResTimeStamp, metadata: VideoFrameMetadata) => any
    ): VideoFrameRequestCallbackId;
    cancelVideoFrameCallback(handle: VideoFrameRequestCallbackId): void;
  }
}

export const SegmentedVideoProcessor: React.FC<VideoProcessorProps> = ({
  segments,
  videoSrc,
  dimensions,
  opacity = 1,
  defaultTransitionDuration = 30,
  fps = 30,
}) => {
  const video = useRef<HTMLVideoElement>(null);
  const canvas = useRef<HTMLCanvasElement>(null);
  const [currentSegmentIndex, setCurrentSegmentIndex] = useState(0);
  const currentFrame = useCurrentFrame();
  const [previousFrame, setPreviousFrame] = useState<ImageData | null>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const { width, height } = dimensions;

  const segmentsInFrames = useMemo(
    () =>
      segments.map((segment) => ({
        ...segment,
        startFrame: secondsToFrames(segment.start, fps),
        endFrame: secondsToFrames(segment.end, fps),
        transitionDurationFrames:
          segment.transition?.durationFrames ?? defaultTransitionDuration,
        cropBox: segment.crop_box,
      })),
    [segments, fps, defaultTransitionDuration]
  );

  const calculateDimensions = useCallback(
    (
      cropBox: CropBox | undefined,
      sourceWidth: number,
      sourceHeight: number
    ): CanvasDimensions => {
      if (!cropBox) {
        return {
          sourceX: 0,
          sourceY: 0,
          sourceWidth: makeEven(sourceWidth),
          sourceHeight: makeEven(sourceHeight),
          x: 0,
          y: 0,
          width: makeEven(width),
          height: makeEven(height),
        };
      }

      // Calculate crop dimensions and make them even
      const cropWidth = makeEven(
        Math.min(sourceWidth, Math.abs(cropBox.x2 - cropBox.x1))
      );
      const cropHeight = makeEven(
        Math.min(sourceHeight, Math.abs(cropBox.y2 - cropBox.y1))
      );
      const x1 = makeEven(Math.max(0, Math.round(cropBox.x1)));
      const y1 = makeEven(Math.max(0, Math.round(cropBox.y1)));

      return {
        sourceX: x1,
        sourceY: y1,
        sourceWidth: cropWidth,
        sourceHeight: cropHeight,
        x: 0,
        y: 0,
        width: makeEven(width),
        height: makeEven(height),
      };
    },
    [width, height]
  );

  const getTransitionProgress = useCallback(
    (
      currentFrame: number,
      currentSegment: ProcessedSegment,
      nextSegment?: ProcessedSegment
    ): number => {
      if (!nextSegment) return 0;

      const transitionStart =
        currentSegment.endFrame - currentSegment.transitionDurationFrames;
      if (currentFrame >= transitionStart) {
        return (
          (currentFrame - transitionStart) /
          currentSegment.transitionDurationFrames
        );
      }
      return 0;
    },
    []
  );

  const processFrame = useCallback(() => {
    if (!canvas.current || !video.current) {
      return;
    }

    const context = canvas.current.getContext("2d");
    if (!context) {
      return;
    }

    const currentSegment = segmentsInFrames[currentSegmentIndex];
    const nextSegment = segmentsInFrames[currentSegmentIndex + 1];

    if (!currentSegment) {
      return;
    }

    // Clear canvas with black background
    context.fillStyle = "#000000";
    context.fillRect(0, 0, width, height);

    const sourceWidth = video.current.videoWidth;
    const sourceHeight = video.current.videoHeight;

    try {
      // Calculate dimensions and draw frame
      const dimensions = calculateDimensions(
        currentSegment.cropBox,
        sourceWidth,
        sourceHeight
      );

      context.drawImage(
        video.current,
        dimensions.sourceX,
        dimensions.sourceY,
        dimensions.sourceWidth,
        dimensions.sourceHeight,
        dimensions.x,
        dimensions.y,
        dimensions.width,
        dimensions.height
      );

      // Get current frame data for transitions
      const currentFrameData = context.getImageData(0, 0, width, height);

      // Handle transitions
      if (currentSegment.transition && nextSegment) {
        const transitionProgress = getTransitionProgress(
          currentFrame,
          currentSegment,
          nextSegment
        );

        if (transitionProgress > 0 && previousFrame) {
          setIsTransitioning(true);
          const transitionEffects = new TransitionEffects(canvas.current);

          switch (currentSegment.transition.type) {
            case "fade":
              transitionEffects.fade(
                currentFrameData,
                previousFrame,
                transitionProgress
              );
              break;
            case "push":
              transitionEffects.push(
                currentFrameData,
                previousFrame,
                transitionProgress,
                currentSegment.transition.options?.pushDirection
              );
              break;
            case "wipe":
              transitionEffects.wipe(
                currentFrameData,
                previousFrame,
                transitionProgress,
                currentSegment.transition.options?.wipeDirection
              );
              break;
            case "zoom":
              transitionEffects.zoom(
                currentFrameData,
                previousFrame,
                transitionProgress,
                currentSegment.transition.options?.zoomAmount
              );
              break;
            case "dissolve":
              transitionEffects.dissolve(
                currentFrameData,
                previousFrame,
                transitionProgress,
                currentSegment.transition.options?.pattern
              );
              break;
          }
        }
      } else {
        setIsTransitioning(false);
      }

      // Store current frame for next transition if not transitioning
      if (!isTransitioning) {
        setPreviousFrame(context.getImageData(0, 0, width, height));
      }

      // Apply global opacity if needed
      if (opacity !== 1) {
        const imageData = context.getImageData(0, 0, width, height);
        for (let i = 0; i < imageData.data.length; i += 4) {
          imageData.data[i + 3] = Math.floor(imageData.data[i + 3] * opacity);
        }
        context.putImageData(imageData, 0, 0);
      }
    } catch (error) {
      console.error("Error processing frame:", error);
    }
  }, [
    width,
    height,
    currentSegmentIndex,
    segmentsInFrames,
    calculateDimensions,
    currentFrame,
    getTransitionProgress,
    opacity,
    previousFrame,
    isTransitioning,
  ]);

  // Synchronize the video with the canvas
  useEffect(() => {
    const { current } = video;
    if (!current?.requestVideoFrameCallback) {
      return;
    }

    let handle = 0;
    const callback = () => {
      processFrame();
      handle = current.requestVideoFrameCallback(callback);
    };

    callback();

    return () => {
      current.cancelVideoFrameCallback(handle);
    };
  }, [processFrame]);

  // Update current segment based on frame
  useEffect(() => {
    const findSegmentForFrame = (frame: number) => {
      let accumulator = 0;
      for (let i = 0; i < segmentsInFrames.length; i++) {
        const segment = segmentsInFrames[i];
        const duration = segment.endFrame - segment.startFrame;
        if (frame < accumulator + duration) {
          if (currentSegmentIndex !== i) {
            setCurrentSegmentIndex(i);
          }
          break;
        }
        accumulator += duration;
      }
    };

    findSegmentForFrame(currentFrame);
  }, [currentFrame, segmentsInFrames, currentSegmentIndex]);

  return (
    <AbsoluteFill>
      <AbsoluteFill>
        <Video ref={video} style={{ opacity: 0 }} src={videoSrc} />
      </AbsoluteFill>
      <AbsoluteFill>
        <canvas
          ref={canvas}
          width={width}
          height={height}
          style={{
            width: "100%",
            height: "100%",
            backgroundColor: "black",
          }}
        />
      </AbsoluteFill>
    </AbsoluteFill>
  );
};
