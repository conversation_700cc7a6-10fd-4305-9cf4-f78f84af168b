import { getVideoMetadata } from "@remotion/media-utils";
import { VideoMetadata } from "@remotion/renderer";
import { Composition, getInputProps } from "remotion";
import { MyComposition } from "./Composition";
import React from "react";
import { IVideoSegment } from "./types";

const a = {
  props: {
    videoSrc: "1fbd1cd6-3610-45fe-b513-80e9bf0bbbe2.mp4",
    video: {
      id: "aa3d9af4-55d3-48d9-9f5e-83bc76e5bcec",
      created_at: "2024-05-11T09:30:05.534335",
      updated_at: "2024-05-11T09:30:58.550915",
      title: "What is OSI Model?",
      file_path:
        "dev/494d9c3d-8121-462d-8b93-acbbfa2478ab/ad6f2215-4a71-46b4-913b-8aad49a3c83e.mp4",
      owner_id: "494d9c3d-8121-462d-8b93-acbbfa2478ab",
      thumbnail_path:
        "dev/494d9c3d-8121-462d-8b93-acbbfa2478ab/2bf0eb5d-97f9-486d-8dbc-0ed24f16f3e6.jpeg",
      media_metadata: {
        file_size_bytes: 15395938,
        height: 1080,
        width: 1920,
        duration_seconds: 502.081,
        fps: 29.97002997002997,
      },
      processing_status: "SUCCESS",
      processing_error: null,
      transcription: {
        segments: [
          {
            id: 1,
            seek: 3000,
            start: 2.8600000000000003,
            end: 6.52,
            text: " OSI stands for Open Systems Interconnection",
            tokens: [
              50365, 12731, 40, 7382, 337, 7238, 27059, 5751, 9826, 313, 50712,
            ],
            temperature: 0,
            avg_logprob: -0.2582097457627119,
            compression_ratio: 1.5142857142857142,
            no_speech_prob: 0.003940582275390625,
          },
          {
            id: 2,
            seek: 3000,
            start: 6.52,
            end: 11.22,
            text: " and is a conceptual framework for how applications communicate over a network.",
            tokens: [
              50713, 293, 307, 257, 24106, 8388, 337, 577, 5821, 7890, 670, 257,
              3209, 13, 50983,
            ],
            temperature: 0,
            avg_logprob: -0.2582097457627119,
            compression_ratio: 1.5142857142857142,
            no_speech_prob: 0.003940582275390625,
          },
          {
            id: 3,
            seek: 3000,
            start: 12.06,
            end: 14.22,
            text: " There are seven layers within the model",
            tokens: [50984, 821, 366, 3407, 7914, 1951, 264, 2316, 51101],
            temperature: 0,
            avg_logprob: -0.2582097457627119,
            compression_ratio: 1.5142857142857142,
            no_speech_prob: 0.003940582275390625,
          },
          {
            id: 4,
            seek: 3000,
            start: 14.74,
            end: 18.76,
            text: " and the layer's depiction is used to help users identify what is happening",
            tokens: [
              51102, 293, 264, 4583, 311, 47740, 307, 1143, 281, 854, 5022,
              5876, 437, 307, 2737, 51324,
            ],
            temperature: 0,
            avg_logprob: -0.2582097457627119,
            compression_ratio: 1.5142857142857142,
            no_speech_prob: 0.003940582275390625,
          },
          {
            id: 5,
            seek: 3000,
            start: 18.76,
            end: 20.4,
            text: " within a networking system.",
            tokens: [51325, 1951, 257, 17985, 1185, 13, 51475],
            temperature: 0,
            avg_logprob: -0.2582097457627119,
            compression_ratio: 1.5142857142857142,
            no_speech_prob: 0.003940582275390625,
          },
          {
            id: 6,
            seek: 5862,
            start: 35.18,
            end: 40.82,
            text: " Before we get started on today's video, if you love our videos, be sure to click the",
            tokens: [
              50365, 4546, 321, 483, 1409, 322, 965, 311, 960, 11, 498, 291,
              959, 527, 2145, 11, 312, 988, 281, 2052, 264, 50906,
            ],
            temperature: 0,
            avg_logprob: -0.114453125,
            compression_ratio: 1.5495049504950495,
            no_speech_prob: 0.0140533447265625,
          },
          {
            id: 7,
            seek: 5862,
            start: 40.82,
            end: 41.7,
            text: " like button below.",
            tokens: [50906, 411, 2960, 2507, 13, 51006],
            temperature: 0,
            avg_logprob: -0.114453125,
            compression_ratio: 1.5495049504950495,
            no_speech_prob: 0.0140533447265625,
          },
          {
            id: 8,
            seek: 5862,
            start: 42.1,
            end: 47.52,
            text: " Then make sure to click subscribe and the little bell to receive notifications of new",
            tokens: [
              51006, 1396, 652, 988, 281, 2052, 3022, 293, 264, 707, 4549, 281,
              4774, 13426, 295, 777, 51242,
            ],
            temperature: 0,
            avg_logprob: -0.114453125,
            compression_ratio: 1.5495049504950495,
            no_speech_prob: 0.0140533447265625,
          },
          {
            id: 9,
            seek: 5862,
            start: 47.52,
            end: 48.38,
            text: " RealPars videos.",
            tokens: [51242, 8467, 47, 685, 2145, 13, 51344],
            temperature: 0,
            avg_logprob: -0.114453125,
            compression_ratio: 1.5495049504950495,
            no_speech_prob: 0.0140533447265625,
          },
          {
            id: 10,
            seek: 5862,
            start: 48.9,
            end: 51.32,
            text: " This way you never miss another one.",
            tokens: [51344, 639, 636, 291, 1128, 1713, 1071, 472, 13, 51601],
            temperature: 0,
            avg_logprob: -0.114453125,
            compression_ratio: 1.5495049504950495,
            no_speech_prob: 0.0140533447265625,
          },
          {
            id: 11,
            seek: 5862,
            start: 54.1,
            end: 58.62,
            text: " The OSI model layers are typically described from the top layer down.",
            tokens: [
              51601, 440, 12731, 40, 2316, 7914, 366, 5850, 7619, 490, 264,
              1192, 4583, 760, 13, 51850,
            ],
            temperature: 0,
            avg_logprob: -0.114453125,
            compression_ratio: 1.5495049504950495,
            no_speech_prob: 0.0140533447265625,
          },
          {
            id: 12,
            seek: 8862,
            start: 58.62,
            end: 69.66,
            text: " The layers are described as application, presentation, session, transport, network, data link, and physical.",
            tokens: [
              50365, 440, 7914, 366, 7619, 382, 3861, 11, 5860, 11, 5481, 11,
              5495, 11, 3209, 11, 1412, 2113, 11, 293, 4001, 13, 50965,
            ],
            temperature: 0,
            avg_logprob: -0.17522321428571427,
            compression_ratio: 1.6076555023923444,
            no_speech_prob: 0.0038814544677734375,
          },
          {
            id: 13,
            seek: 8862,
            start: 70.86,
            end: 77.26,
            text: " These layers are provided by a mixture of network car drivers, operating systems, applications,",
            tokens: [
              50965, 1981, 7914, 366, 5649, 538, 257, 9925, 295, 3209, 1032,
              11590, 11, 7447, 3652, 11, 5821, 11, 51315,
            ],
            temperature: 0,
            avg_logprob: -0.17522321428571427,
            compression_ratio: 1.6076555023923444,
            no_speech_prob: 0.0038814544677734375,
          },
          {
            id: 14,
            seek: 8862,
            start: 77.8,
            end: 86.62,
            text: " and networking hardware that facilitate the transmission of signals over Ethernet, fiber optic, Wi-Fi, or other wireless protocols.",
            tokens: [
              51315, 293, 17985, 8837, 300, 20207, 264, 11574, 295, 12354, 670,
              38636, 7129, 11, 12874, 48269, 11, 14035, 12, 13229, 11, 420, 661,
              14720, 20618, 13, 51815,
            ],
            temperature: 0,
            avg_logprob: -0.17522321428571427,
            compression_ratio: 1.6076555023923444,
            no_speech_prob: 0.0038814544677734375,
          },
          {
            id: 15,
            seek: 11486,
            start: 88.62,
            end: 91.52,
            text: " We'll describe the layers from the top down",
            tokens: [
              50389, 492, 603, 6786, 264, 7914, 490, 264, 1192, 760, 50533,
            ],
            temperature: 0,
            avg_logprob: -0.17158203125,
            compression_ratio: 1.7564766839378239,
            no_speech_prob: 0.0004954338073730469,
          },
          {
            id: 16,
            seek: 11486,
            start: 91.52,
            end: 95.86,
            text: " as the top layer is the application layer or layer 7.",
            tokens: [
              50534, 382, 264, 1192, 4583, 307, 264, 3861, 4583, 420, 4583,
              1614, 13, 50789,
            ],
            temperature: 0,
            avg_logprob: -0.17158203125,
            compression_ratio: 1.7564766839378239,
            no_speech_prob: 0.0004954338073730469,
          },
          {
            id: 17,
            seek: 11486,
            start: 97.12,
            end: 100.88,
            text: " This is the layer that most users interact with and will recognize.",
            tokens: [
              50790, 639, 307, 264, 4583, 300, 881, 5022, 4648, 365, 293, 486,
              5521, 13, 51033,
            ],
            temperature: 0,
            avg_logprob: -0.17158203125,
            compression_ratio: 1.7564766839378239,
            no_speech_prob: 0.0004954338073730469,
          },
          {
            id: 18,
            seek: 11486,
            start: 102,
            end: 106.08,
            text: " The application layer provides network services to the end user.",
            tokens: [
              51034, 440, 3861, 4583, 6417, 3209, 3328, 281, 264, 917, 4195, 13,
              51288,
            ],
            temperature: 0,
            avg_logprob: -0.17158203125,
            compression_ratio: 1.7564766839378239,
            no_speech_prob: 0.0004954338073730469,
          },
          {
            id: 19,
            seek: 11486,
            start: 107.10000000000001,
            end: 111.6,
            text: " These services are protocols that work with the data the client is using.",
            tokens: [
              51289, 1981, 3328, 366, 20618, 300, 589, 365, 264, 1412, 264,
              6423, 307, 1228, 13, 51565,
            ],
            temperature: 0,
            avg_logprob: -0.17158203125,
            compression_ratio: 1.7564766839378239,
            no_speech_prob: 0.0004954338073730469,
          },
          {
            id: 20,
            seek: 11486,
            start: 112.64,
            end: 114.86,
            text: " One of these protocols may be HTTP",
            tokens: [51566, 1485, 295, 613, 20618, 815, 312, 33283, 51730],
            temperature: 0,
            avg_logprob: -0.17158203125,
            compression_ratio: 1.7564766839378239,
            no_speech_prob: 0.0004954338073730469,
          },
          {
            id: 21,
            seek: 14062,
            start: 114.86,
            end: 121.12,
            text: " that is used with web browsers such as Google Chrome, Firefox, and Internet Explorer.",
            tokens: [
              50365, 300, 307, 1143, 365, 3670, 36069, 1270, 382, 3329, 15327,
              11, 46613, 11, 293, 7703, 31895, 13, 50763,
            ],
            temperature: 0,
            avg_logprob: -0.10805457746478873,
            compression_ratio: 1.5817307692307692,
            no_speech_prob: 0.00940704345703125,
          },
          {
            id: 22,
            seek: 14062,
            start: 121.92,
            end: 128.42,
            text: " Other examples of applications that use this layer are Office, Outlook, and Skype.",
            tokens: [
              50763, 5358, 5110, 295, 5821, 300, 764, 341, 4583, 366, 8935, 11,
              5925, 12747, 11, 293, 31743, 13, 51127,
            ],
            temperature: 0,
            avg_logprob: -0.10805457746478873,
            compression_ratio: 1.5817307692307692,
            no_speech_prob: 0.00940704345703125,
          },
          {
            id: 23,
            seek: 14062,
            start: 129.22,
            end: 134.96,
            text: " All of those interactive applications provide a set of services that allow the application",
            tokens: [
              51127, 1057, 295, 729, 15141, 5821, 2893, 257, 992, 295, 3328,
              300, 2089, 264, 3861, 51390,
            ],
            temperature: 0,
            avg_logprob: -0.10805457746478873,
            compression_ratio: 1.5817307692307692,
            no_speech_prob: 0.00940704345703125,
          },
          {
            id: 24,
            seek: 14062,
            start: 134.96,
            end: 140.62,
            text: " layer to supply data to and receive data from the presentation layer.",
            tokens: [
              51390, 4583, 281, 5847, 1412, 281, 293, 4774, 1412, 490, 264,
              5860, 4583, 13, 51799,
            ],
            temperature: 0,
            avg_logprob: -0.10805457746478873,
            compression_ratio: 1.5817307692307692,
            no_speech_prob: 0.00940704345703125,
          },
          {
            id: 25,
            seek: 16454,
            start: 142.5,
            end: 150.2,
            text: " The presentation layer, or layer 6, performs the uncomplicated task of syntax processing,",
            tokens: [
              50365, 440, 5860, 4583, 11, 420, 4583, 1386, 11, 26213, 264, 8585,
              564, 3587, 5633, 295, 28431, 9007, 11, 50890,
            ],
            temperature: 0,
            avg_logprob: -0.1595458984375,
            compression_ratio: 1.5,
            no_speech_prob: 0.003482818603515625,
          },
          {
            id: 26,
            seek: 16454,
            start: 150.6,
            end: 153.42,
            text: " or converting data from one format to another.",
            tokens: [
              50890, 420, 29942, 1412, 490, 472, 7877, 281, 1071, 13, 51084,
            ],
            temperature: 0,
            avg_logprob: -0.1595458984375,
            compression_ratio: 1.5,
            no_speech_prob: 0.003482818603515625,
          },
          {
            id: 27,
            seek: 16454,
            start: 154.18,
            end: 158.68,
            text: " For example, consider you're ordering something from an online store.",
            tokens: [
              51084, 1171, 1365, 11, 1949, 291, 434, 21739, 746, 490, 364, 2950,
              3531, 13, 51331,
            ],
            temperature: 0,
            avg_logprob: -0.1595458984375,
            compression_ratio: 1.5,
            no_speech_prob: 0.003482818603515625,
          },
          {
            id: 28,
            seek: 16454,
            start: 159.24,
            end: 164.54,
            text: " These transactions are typically handled in a secure transmission, which means that the",
            tokens: [
              51331, 1981, 16856, 366, 5850, 18033, 294, 257, 7144, 11574, 11,
              597, 1355, 300, 264, 51562,
            ],
            temperature: 0,
            avg_logprob: -0.1595458984375,
            compression_ratio: 1.5,
            no_speech_prob: 0.003482818603515625,
          },
          {
            id: 29,
            seek: 19216,
            start: 164.54,
            end: 167.92,
            text: " the data passing between the store or the website application",
            tokens: [
              50365, 264, 1412, 8437, 1296, 264, 3531, 420, 264, 3144, 3861,
              50579,
            ],
            temperature: 0,
            avg_logprob: -0.18806475903614459,
            compression_ratio: 1.8888888888888888,
            no_speech_prob: 0.0012836456298828125,
          },
          {
            id: 30,
            seek: 19216,
            start: 168.84,
            end: 171.72,
            text: " will transmit encrypted data to the presentation layer",
            tokens: [
              50580, 486, 17831, 36663, 1412, 281, 264, 5860, 4583, 50749,
            ],
            temperature: 0,
            avg_logprob: -0.18806475903614459,
            compression_ratio: 1.8888888888888888,
            no_speech_prob: 0.0012836456298828125,
          },
          {
            id: 31,
            seek: 19216,
            start: 172.23999999999998,
            end: 174.82,
            text: " that will need to be decrypted and processed.",
            tokens: [
              50750, 300, 486, 643, 281, 312, 979, 627, 25383, 293, 18846, 13,
              50954,
            ],
            temperature: 0,
            avg_logprob: -0.18806475903614459,
            compression_ratio: 1.8888888888888888,
            no_speech_prob: 0.0012836456298828125,
          },
          {
            id: 32,
            seek: 19216,
            start: 175.86,
            end: 179.86,
            text: " This layer handles translating the data from the top layer,",
            tokens: [
              50955, 639, 4583, 18722, 35030, 264, 1412, 490, 264, 1192, 4583,
              11, 51158,
            ],
            temperature: 0,
            avg_logprob: -0.18806475903614459,
            compression_ratio: 1.8888888888888888,
            no_speech_prob: 0.0012836456298828125,
          },
          {
            id: 33,
            seek: 19216,
            start: 180.06,
            end: 182.52,
            text: " which is presented in application format,",
            tokens: [51159, 597, 307, 8212, 294, 3861, 7877, 11, 51298],
            temperature: 0,
            avg_logprob: -0.18806475903614459,
            compression_ratio: 1.8888888888888888,
            no_speech_prob: 0.0012836456298828125,
          },
          {
            id: 34,
            seek: 19216,
            start: 182.8,
            end: 185.68,
            text: " to network format and vice versa.",
            tokens: [51299, 281, 3209, 7877, 293, 11964, 25650, 13, 51539],
            temperature: 0,
            avg_logprob: -0.18806475903614459,
            compression_ratio: 1.8888888888888888,
            no_speech_prob: 0.0012836456298828125,
          },
          {
            id: 35,
            seek: 19216,
            start: 187.64,
            end: 192.16,
            text: " After the presentation layer processes the data from one format to another,",
            tokens: [
              51540, 2381, 264, 5860, 4583, 7555, 264, 1412, 490, 472, 7877,
              281, 1071, 11, 51782,
            ],
            temperature: 0,
            avg_logprob: -0.18806475903614459,
            compression_ratio: 1.8888888888888888,
            no_speech_prob: 0.0012836456298828125,
          },
          {
            id: 36,
            seek: 21656,
            start: 192.16,
            end: 197.24,
            text: " However, the information is then passed to the Session Layer or the Application Layer",
            tokens: [
              50365, 2908, 11, 264, 1589, 307, 550, 4678, 281, 264, 318, 4311,
              35166, 420, 264, 39512, 35166, 50665,
            ],
            temperature: 0,
            avg_logprob: -0.19636194029850745,
            compression_ratio: 1.7166666666666666,
            no_speech_prob: 0.00394439697265625,
          },
          {
            id: 37,
            seek: 21656,
            start: 197.24,
            end: 201.3,
            text: " depending on whether the data is transmitting or receiving.",
            tokens: [
              50665, 5413, 322, 1968, 264, 1412, 307, 7715, 2414, 420, 10040,
              13, 50965,
            ],
            temperature: 0,
            avg_logprob: -0.19636194029850745,
            compression_ratio: 1.7166666666666666,
            no_speech_prob: 0.00394439697265625,
          },
          {
            id: 38,
            seek: 21656,
            start: 204.16,
            end: 206.32,
            text: " At the Session Layer or Layer 5,",
            tokens: [
              50965, 1711, 264, 318, 4311, 35166, 420, 35166, 1025, 11, 51115,
            ],
            temperature: 0,
            avg_logprob: -0.19636194029850745,
            compression_ratio: 1.7166666666666666,
            no_speech_prob: 0.00394439697265625,
          },
          {
            id: 39,
            seek: 21656,
            start: 206.78,
            end: 212.74,
            text: " the construction, direction and conclusion of connections between devices occur.",
            tokens: [
              51115, 264, 6435, 11, 3513, 293, 10063, 295, 9271, 1296, 5759,
              5160, 13, 51465,
            ],
            temperature: 0,
            avg_logprob: -0.19636194029850745,
            compression_ratio: 1.7166666666666666,
            no_speech_prob: 0.00394439697265625,
          },
          {
            id: 40,
            seek: 21656,
            start: 214.16,
            end: 216.56,
            text: " This layer supports multiple types of connections",
            tokens: [51465, 639, 4583, 9346, 3866, 3467, 295, 9271, 51615],
            temperature: 0,
            avg_logprob: -0.19636194029850745,
            compression_ratio: 1.7166666666666666,
            no_speech_prob: 0.00394439697265625,
          },
          {
            id: 41,
            seek: 24308,
            start: 216.56,
            end: 220.84,
            text: " as well as being responsible for authentication and reconnection",
            tokens: [
              50365, 382, 731, 382, 885, 6250, 337, 26643, 293, 30095, 313,
              50565,
            ],
            temperature: 0,
            avg_logprob: -0.12002840909090909,
            compression_ratio: 1.7448979591836735,
            no_speech_prob: 0.0128173828125,
          },
          {
            id: 42,
            seek: 24308,
            start: 220.84,
            end: 222.94,
            text: " if a network interruption should occur.",
            tokens: [50565, 498, 257, 3209, 728, 11266, 820, 5160, 13, 50715],
            temperature: 0,
            avg_logprob: -0.12002840909090909,
            compression_ratio: 1.7448979591836735,
            no_speech_prob: 0.0128173828125,
          },
          {
            id: 43,
            seek: 24308,
            start: 223.96,
            end: 230.38,
            text: " After the session is established, the data then passes to or from the transport layer.",
            tokens: [
              50715, 2381, 264, 5481, 307, 7545, 11, 264, 1412, 550, 11335, 281,
              420, 490, 264, 5495, 4583, 13, 51115,
            ],
            temperature: 0,
            avg_logprob: -0.12002840909090909,
            compression_ratio: 1.7448979591836735,
            no_speech_prob: 0.0128173828125,
          },
          {
            id: 44,
            seek: 24308,
            start: 232.18,
            end: 234.94,
            text: " The transport layer, or layer 4,",
            tokens: [51165, 440, 5495, 4583, 11, 420, 4583, 1017, 11, 51315],
            temperature: 0,
            avg_logprob: -0.12002840909090909,
            compression_ratio: 1.7448979591836735,
            no_speech_prob: 0.0128173828125,
          },
          {
            id: 45,
            seek: 24308,
            start: 235.2,
            end: 239.42,
            text: " is responsible for the transmission of data across network connections.",
            tokens: [
              51315, 307, 6250, 337, 264, 11574, 295, 1412, 2108, 3209, 9271,
              13, 51565,
            ],
            temperature: 0,
            avg_logprob: -0.12002840909090909,
            compression_ratio: 1.7448979591836735,
            no_speech_prob: 0.0128173828125,
          },
          {
            id: 46,
            seek: 24308,
            start: 240.08,
            end: 243.08,
            text: " This layer coordinates how much data to send,",
            tokens: [
              51565, 639, 4583, 21056, 577, 709, 1412, 281, 2845, 11, 51715,
            ],
            temperature: 0,
            avg_logprob: -0.12002840909090909,
            compression_ratio: 1.7448979591836735,
            no_speech_prob: 0.0128173828125,
          },
          {
            id: 47,
            seek: 27006,
            start: 243.08,
            end: 247.26,
            text: " how fast, where it goes, and these sorts of things.",
            tokens: [
              50365, 577, 2370, 11, 689, 309, 1709, 11, 293, 613, 7527, 295,
              721, 13, 50615,
            ],
            temperature: 0,
            avg_logprob: -0.11707899305555555,
            compression_ratio: 1.595959595959596,
            no_speech_prob: 0.00279998779296875,
          },
          {
            id: 48,
            seek: 27006,
            start: 247.96,
            end: 252.02,
            text: " Of the most widely known protocols for internet applications,",
            tokens: [
              50615, 2720, 264, 881, 13371, 2570, 20618, 337, 4705, 5821, 11,
              50815,
            ],
            temperature: 0,
            avg_logprob: -0.11707899305555555,
            compression_ratio: 1.595959595959596,
            no_speech_prob: 0.00279998779296875,
          },
          {
            id: 49,
            seek: 27006,
            start: 252.66,
            end: 256.16,
            text: " these services may be provided by Transmission Control Protocol",
            tokens: [
              50815, 613, 3328, 815, 312, 5649, 538, 6531, 29797, 12912, 48753,
              51115,
            ],
            temperature: 0,
            avg_logprob: -0.11707899305555555,
            compression_ratio: 1.595959595959596,
            no_speech_prob: 0.00279998779296875,
          },
          {
            id: 50,
            seek: 27006,
            start: 256.16,
            end: 261.04,
            text: " and User Datagram Protocol .",
            tokens: [51115, 293, 32127, 9315, 3914, 48753, 2411, 51315],
            temperature: 0,
            avg_logprob: -0.11707899305555555,
            compression_ratio: 1.595959595959596,
            no_speech_prob: 0.00279998779296875,
          },
          {
            id: 51,
            seek: 27006,
            start: 262.06,
            end: 265.32,
            text: " Other protocols may provide additional capabilities,",
            tokens: [51315, 5358, 20618, 815, 2893, 4497, 10862, 11, 51465],
            temperature: 0,
            avg_logprob: -0.11707899305555555,
            compression_ratio: 1.595959595959596,
            no_speech_prob: 0.00279998779296875,
          },
          {
            id: 52,
            seek: 27006,
            start: 265.66,
            end: 270.06,
            text: " including error recovery, data flow, and retransmission.",
            tokens: [
              51465, 3009, 6713, 8597, 11, 1412, 3095, 11, 293, 23106, 599,
              29797, 13, 51765,
            ],
            temperature: 0,
            avg_logprob: -0.11707899305555555,
            compression_ratio: 1.595959595959596,
            no_speech_prob: 0.00279998779296875,
          },
          {
            id: 53,
            seek: 29344,
            start: 271.04,
            end: 278.54,
            text: " Once the transport layer has completed its function, the data is then passed to or from the network layer.",
            tokens: [
              50365, 3443, 264, 5495, 4583, 575, 7365, 1080, 2445, 11, 264,
              1412, 307, 550, 4678, 281, 420, 490, 264, 3209, 4583, 13, 50815,
            ],
            temperature: 0,
            avg_logprob: -0.*****************,
            compression_ratio: 1.****************,
            no_speech_prob: 0.****************,
          },
          {
            id: 54,
            seek: 29344,
            start: 280.**************,
            end: 285.3,
            text: " The network layer, or layer 3, handles the routing of the data.",
            tokens: [
              50815, 440, 3209, 4583, 11, 420, 4583, 805, 11, 18722, 264, 32722,
              295, 264, 1412, 13, 51115,
            ],
            temperature: 0,
            avg_logprob: -0.*****************,
            compression_ratio: 1.****************,
            no_speech_prob: 0.****************,
          },
          {
            id: 55,
            seek: 29344,
            start: 286.04,
            end: 293.44,
            text: " After the data arrives at this layer, each frame of data is examined to conclude if the data has reached its ultimate target.",
            tokens: [
              51115, 2381, 264, 1412, 20116, 412, 341, 4583, 11, 1184, 3920,
              295, 1412, 307, 30972, 281, 16886, 498, 264, 1412, 575, 6488,
              1080, 9705, 3779, 13, 51515,
            ],
            temperature: 0,
            avg_logprob: -0.*****************,
            compression_ratio: 1.****************,
            no_speech_prob: 0.****************,
          },
          {
            id: 56,
            seek: 32264,
            start: 293.44,
            end: 298.44,
            text: " The layer sends data to the correct destination on outgoing transmissions",
            tokens: [
              50365, 440, 4583, 14790, 1412, 281, 264, 3006, 12236, 322, 41565,
              7715, 7922, 50615,
            ],
            temperature: 0,
            avg_logprob: -0.09069511217948718,
            compression_ratio: 1.6561085972850678,
            no_speech_prob: 0.00498199462890625,
          },
          {
            id: 57,
            seek: 32264,
            start: 299.02,
            end: 301.82,
            text: " and receives incoming transmissions as well.",
            tokens: [50615, 293, 20717, 22341, 7715, 7922, 382, 731, 13, 50765],
            temperature: 0,
            avg_logprob: -0.09069511217948718,
            compression_ratio: 1.6561085972850678,
            no_speech_prob: 0.00498199462890625,
          },
          {
            id: 58,
            seek: 32264,
            start: 302.42,
            end: 308.8,
            text: " The IP portion of TCP IP is the commonly known network layer for the internet.",
            tokens: [
              50765, 440, 8671, 8044, 295, 48965, 8671, 307, 264, 12719, 2570,
              3209, 4583, 337, 264, 4705, 13, 51165,
            ],
            temperature: 0,
            avg_logprob: -0.09069511217948718,
            compression_ratio: 1.6561085972850678,
            no_speech_prob: 0.00498199462890625,
          },
          {
            id: 59,
            seek: 32264,
            start: 309.94,
            end: 315.44,
            text: " This layer also manages the mapping between logical addresses and physical addresses.",
            tokens: [
              51165, 639, 4583, 611, 22489, 264, 18350, 1296, 14978, 16862, 293,
              4001, 16862, 13, 51465,
            ],
            temperature: 0,
            avg_logprob: -0.09069511217948718,
            compression_ratio: 1.6561085972850678,
            no_speech_prob: 0.00498199462890625,
          },
          {
            id: 60,
            seek: 32264,
            start: 316.38,
            end: 322.64,
            text: " For IP addresses, this is accomplished through Address Resolution Protocol or ARP.",
            tokens: [
              51465, 1171, 8671, 16862, 11, 341, 307, 15419, 807, 5349, 735,
              5015, 3386, 48753, 420, 8943, 47, 13, 51815,
            ],
            temperature: 0,
            avg_logprob: -0.09069511217948718,
            compression_ratio: 1.6561085972850678,
            no_speech_prob: 0.00498199462890625,
          },
          {
            id: 61,
            seek: 34994,
            start: 323.59999999999997,
            end: 328.68,
            text: " The data is then passed to the next required layer, which is the data link layer.",
            tokens: [
              50365, 440, 1412, 307, 550, 4678, 281, 264, 958, 4739, 4583, 11,
              597, 307, 264, 1412, 2113, 4583, 13, 50798,
            ],
            temperature: 0,
            avg_logprob: -0.12334558823529412,
            compression_ratio: 1.6649746192893402,
            no_speech_prob: 0.0026721954345703125,
          },
          {
            id: 62,
            seek: 34994,
            start: 330.52,
            end: 336.24,
            text: " The data link layer, or layer 2, is considered the most complex of the layers.",
            tokens: [
              50798, 440, 1412, 2113, 4583, 11, 420, 4583, 568, 11, 307, 4888,
              264, 881, 3997, 295, 264, 7914, 13, 51107,
            ],
            temperature: 0,
            avg_logprob: -0.12334558823529412,
            compression_ratio: 1.6649746192893402,
            no_speech_prob: 0.0026721954345703125,
          },
          {
            id: 63,
            seek: 34994,
            start: 336.68,
            end: 343.92,
            text: " This layer is often divided into sub-layers called Media Access Control, or MAC, and Logical",
            tokens: [
              51107, 639, 4583, 307, 2049, 6666, 666, 1422, 12, 8376, 433, 1219,
              14741, 17166, 12912, 11, 420, 27716, 11, 293, 10824, 804, 51432,
            ],
            temperature: 0,
            avg_logprob: -0.12334558823529412,
            compression_ratio: 1.6649746192893402,
            no_speech_prob: 0.0026721954345703125,
          },
          {
            id: 64,
            seek: 34994,
            start: 343.92,
            end: 345.7,
            text: " Link Control, or LLC.",
            tokens: [51432, 8466, 12912, 11, 420, 33698, 13, 51604],
            temperature: 0,
            avg_logprob: -0.12334558823529412,
            compression_ratio: 1.6649746192893402,
            no_speech_prob: 0.0026721954345703125,
          },
          {
            id: 65,
            seek: 34994,
            start: 346.68,
            end: 349.94,
            text: " The layer sets up links across the physical network.",
            tokens: [
              51604, 440, 4583, 6352, 493, 6123, 2108, 264, 4001, 3209, 13,
              51785,
            ],
            temperature: 0,
            avg_logprob: -0.12334558823529412,
            compression_ratio: 1.6649746192893402,
            no_speech_prob: 0.0026721954345703125,
          },
          {
            id: 66,
            seek: 37434,
            start: 349.94,
            end: 358.52,
            text: " When this layer receives data from the physical layer, it checks for transmission errors and then packages the bits into data frames.",
            tokens: [
              50365, 1133, 341, 4583, 20717, 1412, 490, 264, 4001, 4583, 11,
              309, 13834, 337, 11574, 13603, 293, 550, 17401, 264, 9239, 666,
              1412, 12083, 13, 50815,
            ],
            temperature: 0,
            avg_logprob: -0.08806295955882353,
            compression_ratio: 1.5522388059701493,
            no_speech_prob: 0.006191253662109375,
          },
          {
            id: 67,
            seek: 37434,
            start: 359.32,
            end: 365.9,
            text: " From there, this layer manages the physical addressing methods for the MAC or LLC layers.",
            tokens: [
              50815, 3358, 456, 11, 341, 4583, 22489, 264, 4001, 14329, 7150,
              337, 264, 27716, 420, 33698, 7914, 13, 51165,
            ],
            temperature: 0,
            avg_logprob: -0.08806295955882353,
            compression_ratio: 1.5522388059701493,
            no_speech_prob: 0.006191253662109375,
          },
          {
            id: 68,
            seek: 37434,
            start: 366.44,
            end: 374.34,
            text: " An example of the MAC layer includes 802.11 wireless specifications as well as Ethernet.",
            tokens: [
              51165, 1107, 1365, 295, 264, 27716, 4583, 5974, 4688, 17, 13,
              5348, 14720, 29448, 382, 731, 382, 38636, 7129, 13, 51615,
            ],
            temperature: 0,
            avg_logprob: -0.08806295955882353,
            compression_ratio: 1.5522388059701493,
            no_speech_prob: 0.006191253662109375,
          },
          {
            id: 69,
            seek: 39900,
            start: 374.34,
            end: 383.62,
            text: " At the data link layer, the data passes to or from the final layer in the OSI model, which is the physical layer.",
            tokens: [
              50365, 1711, 264, 1412, 2113, 4583, 11, 264, 1412, 11335, 281,
              420, 490, 264, 2572, 4583, 294, 264, 12731, 40, 2316, 11, 597,
              307, 264, 4001, 4583, 13, 50840,
            ],
            temperature: 0,
            avg_logprob: -0.15669014084507044,
            compression_ratio: 1.7093023255813953,
            no_speech_prob: 0.0013666152954101562,
          },
          {
            id: 70,
            seek: 39900,
            start: 385.46000000000004,
            end: 391.38,
            text: " The physical layer, or layer 1, is the electrical or physical layer of the model.",
            tokens: [
              50926, 440, 4001, 4583, 11, 420, 4583, 502, 11, 307, 264, 12147,
              420, 4001, 4583, 295, 264, 2316, 13, 51221,
            ],
            temperature: 0,
            avg_logprob: -0.15669014084507044,
            compression_ratio: 1.7093023255813953,
            no_speech_prob: 0.0013666152954101562,
          },
          {
            id: 71,
            seek: 39900,
            start: 392.40000000000003,
            end: 399,
            text: " This layer encompasses the network cables, power plugs, cable pinouts, wireless radio frequencies,",
            tokens: [
              51268, 639, 4583, 49866, 264, 3209, 17555, 11, 1347, 33899, 11,
              8220, 5447, 7711, 11, 14720, 6477, 20250, 11, 51608,
            ],
            temperature: 0,
            avg_logprob: -0.15669014084507044,
            compression_ratio: 1.7093023255813953,
            no_speech_prob: 0.0013666152954101562,
          },
          {
            id: 72,
            seek: 42508,
            start: 399,
            end: 407.62,
            text: " connectors, transceivers, receivers, repeaters, pulses of light, electric voltages, etc.",
            tokens: [
              50365, 31865, 11, 1145, 384, 1762, 11, 49196, 11, 7149, 433, 11,
              45279, 295, 1442, 11, 5210, 49614, 11, 5183, 13, 50865,
            ],
            temperature: 0,
            avg_logprob: -0.10040713028169014,
            compression_ratio: 1.****************,
            no_speech_prob: 0.0151824951171875,
          },
          {
            id: 73,
            seek: 42508,
            start: 408.98,
            end: 413.48,
            text: " When troubleshooting problems, this is typically the first place to start.",
            tokens: [
              50865, 1133, 15379, 47011, 2740, 11, 341, 307, 5850, 264, 700,
              1081, 281, 722, 13, 51115,
            ],
            temperature: 0,
            avg_logprob: -0.10040713028169014,
            compression_ratio: 1.****************,
            no_speech_prob: 0.0151824951171875,
          },
          {
            id: 74,
            seek: 42508,
            start: 414.06,
            end: 418.28,
            text: " Is the device plugged in? Is the wireless card working?",
            tokens: [
              51115, 1119, 264, 4302, 25679, 294, 30, 1119, 264, 14720, 2920,
              1364, 30, 51415,
            ],
            temperature: 0,
            avg_logprob: -0.10040713028169014,
            compression_ratio: 1.****************,
            no_speech_prob: 0.0151824951171875,
          },
          {
            id: 75,
            seek: 42508,
            start: 420.32,
            end: 425.08,
            text: " At this layer, the model transmits the digital data bits from the source",
            tokens: [
              51415, 1711, 341, 4583, 11, 264, 2316, 7715, 1208, 264, 4562,
              1412, 9239, 490, 264, 4009, 51665,
            ],
            temperature: 0,
            avg_logprob: -0.10040713028169014,
            compression_ratio: 1.****************,
            no_speech_prob: 0.0151824951171875,
          },
          {
            id: 76,
            seek: 45130,
            start: 425.08,
            end: 429.58,
            text: " or sending devices physical layer over network communications media",
            tokens: [
              50365, 420, 7750, 5759, 4001, 4583, 670, 3209, 15163, 3021, 50615,
            ],
            temperature: 0,
            avg_logprob: -0.12014358108108109,
            compression_ratio: 1.6803652968036529,
            no_speech_prob: 0.01690673828125,
          },
          {
            id: 77,
            seek: 45130,
            start: 429.58,
            end: 433.22,
            text: " which can be electrical, mechanical, or radio",
            tokens: [
              50615, 597, 393, 312, 12147, 11, 12070, 11, 420, 6477, 50815,
            ],
            temperature: 0,
            avg_logprob: -0.12014358108108109,
            compression_ratio: 1.6803652968036529,
            no_speech_prob: 0.01690673828125,
          },
          {
            id: 78,
            seek: 45130,
            start: 433.22,
            end: 437.04,
            text: " to the receiving or destination devices physical layer.",
            tokens: [
              50815, 281, 264, 10040, 420, 12236, 5759, 4001, 4583, 13, 51065,
            ],
            temperature: 0,
            avg_logprob: -0.12014358108108109,
            compression_ratio: 1.6803652968036529,
            no_speech_prob: 0.01690673828125,
          },
          {
            id: 79,
            seek: 45130,
            start: 438.84000000000003,
            end: 442.68,
            text: " The OSI model is a guide for developers and vendors",
            tokens: [
              51065, 440, 12731, 40, 2316, 307, 257, 5934, 337, 8849, 293,
              22056, 51265,
            ],
            temperature: 0,
            avg_logprob: -0.12014358108108109,
            compression_ratio: 1.6803652968036529,
            no_speech_prob: 0.01690673828125,
          },
          {
            id: 80,
            seek: 45130,
            start: 442.68,
            end: 447.54,
            text: " to smooth the progress of developing communication products and software programs",
            tokens: [
              51265, 281, 5508, 264, 4205, 295, 6416, 6101, 3383, 293, 4722,
              4268, 51515,
            ],
            temperature: 0,
            avg_logprob: -0.12014358108108109,
            compression_ratio: 1.6803652968036529,
            no_speech_prob: 0.01690673828125,
          },
          {
            id: 81,
            seek: 45130,
            start: 448.08,
            end: 451.3,
            text: " that will work in cooperation with a commonly established model.",
            tokens: [
              51515, 300, 486, 589, 294, 14968, 365, 257, 12719, 7545, 2316, 13,
              51715,
            ],
            temperature: 0,
            avg_logprob: -0.12014358108108109,
            compression_ratio: 1.6803652968036529,
            no_speech_prob: 0.01690673828125,
          },
          {
            id: 82,
            seek: 48130,
            start: 451.3,
            end: 459.64,
            text: " Once you understand the model, you can then understand which protocols and devices will be compatible with one another.",
            tokens: [
              50365, 3443, 291, 1223, 264, 2316, 11, 291, 393, 550, 1223, 597,
              20618, 293, 5759, 486, 312, 18218, 365, 472, 1071, 13, 50865,
            ],
            temperature: 0,
            avg_logprob: -0.13389008620689655,
            compression_ratio: 1.4792899408284024,
            no_speech_prob: 0.002147674560546875,
          },
          {
            id: 83,
            seek: 48130,
            start: 464.96,
            end: 472.16,
            text: " Want to learn PLC programming in an easy to understand format and take your career to the next level?",
            tokens: [
              51015, 11773, 281, 1466, 6999, 34, 9410, 294, 364, 1858, 281,
              1223, 7877, 293, 747, 428, 3988, 281, 264, 958, 1496, 30, 51415,
            ],
            temperature: 0,
            avg_logprob: -0.13389008620689655,
            compression_ratio: 1.4792899408284024,
            no_speech_prob: 0.002147674560546875,
          },
          {
            id: 84,
            seek: 48130,
            start: 473.32,
            end: 476.54,
            text: " Head on over to realpars.com",
            tokens: [
              51415, 11398, 322, 670, 281, 957, 79, 685, 13, 1112, 51665,
            ],
            temperature: 0,
            avg_logprob: -0.13389008620689655,
            compression_ratio: 1.4792899408284024,
            no_speech_prob: 0.002147674560546875,
          },
          {
            id: 85,
            seek: 50207,
            start: 481.3,
            end: 481.46,
            text: " Thank you.",
            tokens: [50365, 1044, 291, 13, 51403],
            temperature: 0,
            avg_logprob: -0.6608072916666666,
            compression_ratio: 0.5555555555555556,
            no_speech_prob: 0.5498046875,
          },
        ],
        detected_language: "en",
        transcription:
          "OSI stands for Open Systems Interconnection and is a conceptual framework for how applications communicate over a network. There are seven layers within the model and the layer's depiction is used to help users identify what is happening within a networking system. Before we get started on today's video, if you love our videos, be sure to click the like button below. Then make sure to click subscribe and the little bell to receive notifications of new RealPars videos. This way you never miss another one. The OSI model layers are typically described from the top layer down. The layers are described as application, presentation, session, transport, network, data link, and physical. These layers are provided by a mixture of network car drivers, operating systems, applications, and networking hardware that facilitate the transmission of signals over Ethernet, fiber optic, Wi-Fi, or other wireless protocols. We'll describe the layers from the top down as the top layer is the application layer or layer 7. This is the layer that most users interact with and will recognize. The application layer provides network services to the end user. These services are protocols that work with the data the client is using. One of these protocols may be HTTP that is used with web browsers such as Google Chrome, Firefox, and Internet Explorer. Other examples of applications that use this layer are Office, Outlook, and Skype. All of those interactive applications provide a set of services that allow the application layer to supply data to and receive data from the presentation layer. The presentation layer, or layer 6, performs the uncomplicated task of syntax processing, or converting data from one format to another. For example, consider you're ordering something from an online store. These transactions are typically handled in a secure transmission, which means that the the data passing between the store or the website application will transmit encrypted data to the presentation layer that will need to be decrypted and processed. This layer handles translating the data from the top layer, which is presented in application format, to network format and vice versa. After the presentation layer processes the data from one format to another, However, the information is then passed to the Session Layer or the Application Layer depending on whether the data is transmitting or receiving. At the Session Layer or Layer 5, the construction, direction and conclusion of connections between devices occur. This layer supports multiple types of connections as well as being responsible for authentication and reconnection if a network interruption should occur. After the session is established, the data then passes to or from the transport layer. The transport layer, or layer 4, is responsible for the transmission of data across network connections. This layer coordinates how much data to send, how fast, where it goes, and these sorts of things. Of the most widely known protocols for internet applications, these services may be provided by Transmission Control Protocol and User Datagram Protocol . Other protocols may provide additional capabilities, including error recovery, data flow, and retransmission. Once the transport layer has completed its function, the data is then passed to or from the network layer. The network layer, or layer 3, handles the routing of the data. After the data arrives at this layer, each frame of data is examined to conclude if the data has reached its ultimate target. The layer sends data to the correct destination on outgoing transmissions and receives incoming transmissions as well. The IP portion of TCP IP is the commonly known network layer for the internet. This layer also manages the mapping between logical addresses and physical addresses. For IP addresses, this is accomplished through Address Resolution Protocol or ARP. The data is then passed to the next required layer, which is the data link layer. The data link layer, or layer 2, is considered the most complex of the layers. This layer is often divided into sub-layers called Media Access Control, or MAC, and Logical Link Control, or LLC. The layer sets up links across the physical network. When this layer receives data from the physical layer, it checks for transmission errors and then packages the bits into data frames. From there, this layer manages the physical addressing methods for the MAC or LLC layers. An example of the MAC layer includes 802.11 wireless specifications as well as Ethernet. At the data link layer, the data passes to or from the final layer in the OSI model, which is the physical layer. The physical layer, or layer 1, is the electrical or physical layer of the model. This layer encompasses the network cables, power plugs, cable pinouts, wireless radio frequencies, connectors, transceivers, receivers, repeaters, pulses of light, electric voltages, etc. When troubleshooting problems, this is typically the first place to start. Is the device plugged in? Is the wireless card working? At this layer, the model transmits the digital data bits from the source or sending devices physical layer over network communications media which can be electrical, mechanical, or radio to the receiving or destination devices physical layer. The OSI model is a guide for developers and vendors to smooth the progress of developing communication products and software programs that will work in cooperation with a commonly established model. Once you understand the model, you can then understand which protocols and devices will be compatible with one another. Want to learn PLC programming in an easy to understand format and take your career to the next level? Head on over to realpars.com Thank you.",
        translation: null,
        device: "cuda",
        model: "large-v3",
      },
    },
    clipMetadata: {
      fps: 30000,
      width: 1920,
      height: 1080,
      durationInSeconds: 65.031633,
      codec: "h264",
      canPlayInVideoTag: true,
      supportsSeeking: false,
      colorSpace: "bt709",
      audioCodec: "aac",
      audioFileExtension: "aac",
    },
    canvasDimensions: { width: 1920, height: 1080 },
  },
};

// const fps = 30;
// const defaultConf = {
//   inputProps: {
//     config: {
//       width: 1,
//       height: 1,
//       fontSize: 30,
//       fontFamily: "Georgia",
//       videoSrc:
//         "https://experimental-files-transcribe.ams3.digitaloceanspaces.com/%D0%A1%D0%A2%D0%A0%D0%90%D0%A2%D0%98%D0%9C%D0%98%D0%A0%20%D0%BE%D1%82%20%D0%98%D0%B3%D1%80%D0%B8%20%D0%BD%D0%B0%20%D0%92%D0%BE%D0%BB%D1%8F%D1%82%D0%B0%20%D0%B3%D0%BE%D1%81%D1%82%D1%83%D0%B2%D0%B0%20%D0%BD%D0%B0%20%D0%98%D0%B2%D0%B0%D0%BD%20%D0%9A%D0%B8%D1%80%D0%BA%D0%BE%D0%B2.mp4",
//       segments: [],
//       subColor: "white",
//       subBackground: "black",
//       highlightBgColor: "purple",
//     },
//   },
//   outputLocation: "out.mp4",
// };
const { props } = getInputProps() as {
  props?: any;
};
const { clipMetadata, compositionDimensions } = props || {}; // Provide an empty object as default if props is undefined

const durationInSeconds = props.segments.reduce(
  (acc: number, segment: IVideoSegment) => {
    return acc + (segment.end - segment.start);
  },
  0
);

// Using optional chaining to safely access properties
const width = clipMetadata?.width;
const height = clipMetadata?.height;
const fps = clipMetadata?.fps;
export const RemotionRoot: React.FC = () => {
  // @ts-ignore
  return (
    <>
      <Composition
        id="MyComp"
        component={MyComposition}
        fps={fps}
        durationInFrames={Math.floor(durationInSeconds * fps)}
        width={compositionDimensions.width}
        height={compositionDimensions.height}
        // @ts-ignore
        defaultProps={{ props: a }}
        // schema={compSchema}
        // calculateMetadata={async ({ props }) => {
        //   console.warn("propskata", props);
        //   // @ts-ignore
        //   const config = props.config;
        //   // If props.isRendering:
        //   const { height, width, durationInSeconds } = await getVideoMetadata(
        //     config.videoSrc
        //   );
        //   return {
        //     durationInFrames: Math.floor(durationInSeconds * fps),
        //     height,
        //     width,
        //   };
        // }}
      />
    </>
  );
};
