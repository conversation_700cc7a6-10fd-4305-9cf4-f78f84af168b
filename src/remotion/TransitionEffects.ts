import { PushDirection, WipeDirection } from "./types";

export class TransitionEffects {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private width: number;
  private height: number;

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.ctx = canvas.getContext("2d")!;
    this.width = canvas.width;
    this.height = canvas.height;
  }

  private clear() {
    this.ctx.clearRect(0, 0, this.width, this.height);
  }

  public fade(
    currentFrame: ImageData,
    previousFrame: ImageData,
    progress: number
  ) {
    const tempCanvas = document.createElement("canvas");
    tempCanvas.width = this.width;
    tempCanvas.height = this.height;
    const tempCtx = tempCanvas.getContext("2d")!;

    // Draw current frame with fading in
    this.ctx.putImageData(currentFrame, 0, 0);
    this.ctx.globalAlpha = progress;

    // Draw previous frame with fading out
    tempCtx.putImageData(previousFrame, 0, 0);
    this.ctx.drawImage(tempCanvas, 0, 0);
    this.ctx.globalAlpha = 1 - progress;

    this.ctx.globalAlpha = 1;
  }

  public push(
    currentFrame: ImageData,
    previousFrame: ImageData,
    progress: number,
    direction: PushDirection = "left"
  ) {
    this.clear();

    const xOffset =
      direction === "left"
        ? -this.width * (1 - progress)
        : direction === "right"
        ? this.width * (1 - progress)
        : 0;
    const yOffset =
      direction === "up"
        ? -this.height * (1 - progress)
        : direction === "down"
        ? this.height * (1 - progress)
        : 0;

    // Draw previous frame
    const tempCanvas1 = document.createElement("canvas");
    tempCanvas1.width = this.width;
    tempCanvas1.height = this.height;
    const tempCtx1 = tempCanvas1.getContext("2d")!;
    tempCtx1.putImageData(previousFrame, 0, 0);
    this.ctx.drawImage(tempCanvas1, -xOffset, -yOffset);

    // Draw current frame
    const tempCanvas2 = document.createElement("canvas");
    tempCanvas2.width = this.width;
    tempCanvas2.height = this.height;
    const tempCtx2 = tempCanvas2.getContext("2d")!;
    tempCtx2.putImageData(currentFrame, 0, 0);
    this.ctx.drawImage(
      tempCanvas2,
      this.width * (1 - progress) * (direction === "left" ? 1 : -1),
      this.height * (1 - progress) * (direction === "up" ? 1 : -1)
    );
  }

  public wipe(
    currentFrame: ImageData,
    previousFrame: ImageData,
    progress: number,
    direction: WipeDirection = "left"
  ) {
    this.clear();

    // Draw previous frame first
    this.ctx.putImageData(previousFrame, 0, 0);

    // Create clipping path for wipe effect
    this.ctx.save();
    this.ctx.beginPath();

    if (direction === "diagonal") {
      const diagonal = Math.sqrt(
        this.width * this.width + this.height * this.height
      );
      const angle = Math.atan2(this.height, this.width);
      const progress_diagonal = diagonal * progress;

      this.ctx.moveTo(0, 0);
      this.ctx.lineTo(
        progress_diagonal * Math.cos(angle),
        progress_diagonal * Math.sin(angle)
      );
      this.ctx.lineTo(0, progress_diagonal * Math.sin(angle));
      this.ctx.lineTo(0, 0);
    } else {
      switch (direction) {
        case "left":
          this.ctx.rect(0, 0, this.width * progress, this.height);
          break;
        case "right":
          this.ctx.rect(
            this.width * (1 - progress),
            0,
            this.width * progress,
            this.height
          );
          break;
        case "up":
          this.ctx.rect(0, 0, this.width, this.height * progress);
          break;
        case "down":
          this.ctx.rect(
            0,
            this.height * (1 - progress),
            this.width,
            this.height * progress
          );
          break;
      }
    }

    this.ctx.clip();
    this.ctx.putImageData(currentFrame, 0, 0);
    this.ctx.restore();
  }

  public zoom(
    currentFrame: ImageData,
    previousFrame: ImageData,
    progress: number,
    zoomAmount: number = 1.5
  ) {
    this.clear();

    // Draw previous frame zooming out
    const prevScale = 1 + (zoomAmount - 1) * progress;
    const prevX = (this.width - this.width * prevScale) / 2;
    const prevY = (this.height - this.height * prevScale) / 2;

    const tempCanvas1 = document.createElement("canvas");
    tempCanvas1.width = this.width;
    tempCanvas1.height = this.height;
    const tempCtx1 = tempCanvas1.getContext("2d")!;
    tempCtx1.putImageData(previousFrame, 0, 0);

    this.ctx.globalAlpha = 1 - progress;
    this.ctx.drawImage(
      tempCanvas1,
      prevX,
      prevY,
      this.width * prevScale,
      this.height * prevScale
    );

    // Draw current frame zooming in
    const currScale = zoomAmount - (zoomAmount - 1) * progress;
    const currX = (this.width - this.width * currScale) / 2;
    const currY = (this.height - this.height * currScale) / 2;

    const tempCanvas2 = document.createElement("canvas");
    tempCanvas2.width = this.width;
    tempCanvas2.height = this.height;
    const tempCtx2 = tempCanvas2.getContext("2d")!;
    tempCtx2.putImageData(currentFrame, 0, 0);

    this.ctx.globalAlpha = progress;
    this.ctx.drawImage(
      tempCanvas2,
      currX,
      currY,
      this.width * currScale,
      this.height * currScale
    );

    this.ctx.globalAlpha = 1;
  }

  public dissolve(
    currentFrame: ImageData,
    previousFrame: ImageData,
    progress: number,
    pattern: string = "random"
  ) {
    this.clear();

    const patternCanvas = document.createElement("canvas");
    patternCanvas.width = this.width;
    patternCanvas.height = this.height;
    const patternCtx = patternCanvas.getContext("2d")!;

    switch (pattern) {
      case "diamonds":
        this.generateDiamondPattern(patternCtx, progress);
        break;
      case "circles":
        this.generateCirclePattern(patternCtx, progress);
        break;
      default:
        this.generateRandomPattern(patternCtx, progress);
    }

    // Draw previous frame
    this.ctx.putImageData(previousFrame, 0, 0);

    // Apply pattern as mask
    this.ctx.globalCompositeOperation = "source-atop";
    this.ctx.drawImage(patternCanvas, 0, 0);

    // Draw current frame
    const tempCanvas = document.createElement("canvas");
    tempCanvas.width = this.width;
    tempCanvas.height = this.height;
    const tempCtx = tempCanvas.getContext("2d")!;
    tempCtx.putImageData(currentFrame, 0, 0);

    this.ctx.globalCompositeOperation = "source-over";
    this.ctx.drawImage(tempCanvas, 0, 0);
  }

  private generateRandomPattern(
    ctx: CanvasRenderingContext2D,
    progress: number
  ) {
    const imageData = ctx.createImageData(this.width, this.height);
    const threshold = progress * 255;

    for (let i = 0; i < imageData.data.length; i += 4) {
      const random = Math.random() * 255;
      const alpha = random < threshold ? 255 : 0;
      imageData.data[i + 3] = alpha;
    }

    ctx.putImageData(imageData, 0, 0);
  }

  private generateDiamondPattern(
    ctx: CanvasRenderingContext2D,
    progress: number
  ) {
    const size = 30;
    const numDiamonds = Math.ceil((this.width * this.height) / (size * size));
    const diamondsToShow = Math.floor(numDiamonds * progress);

    ctx.fillStyle = "black";

    for (let i = 0; i < diamondsToShow; i++) {
      const x = (i * size) % this.width;
      const y = Math.floor((i * size) / this.width) * size;

      ctx.beginPath();
      ctx.moveTo(x + size / 2, y);
      ctx.lineTo(x + size, y + size / 2);
      ctx.lineTo(x + size / 2, y + size);
      ctx.lineTo(x, y + size / 2);
      ctx.closePath();
      ctx.fill();
    }
  }

  private generateCirclePattern(
    ctx: CanvasRenderingContext2D,
    progress: number
  ) {
    const radius = 20;
    const numCircles = Math.ceil(
      (this.width * this.height) / (Math.PI * radius * radius)
    );
    const circlesToShow = Math.floor(numCircles * progress);

    ctx.fillStyle = "black";

    for (let i = 0; i < circlesToShow; i++) {
      const x = (i * radius * 2) % this.width;
      const y = Math.floor((i * radius * 2) / this.width) * radius * 2;

      ctx.beginPath();
      ctx.arc(x + radius, y + radius, radius, 0, Math.PI * 2);
      ctx.fill();
    }
  }
}
