export type IClipConfig = {
  segments: IVideoSegment[];
  subtitles: ISubtitles;
  sourceVideo: IVideo;
  compositionDimensions: {
    width: number;
    height: number;
  };
  renderMode: boolean;
  videoElement?: HTMLVideoElement | null;
  scale?: number;
  configUpdate: (config: Partial<IClipConfig>) => void;
};

export type Preset = {
  id: string;
  name: string;
  config: PresetConfig;
  created_at: string;
  updated_at: string;
};

export type PresetConfig = {
  words_per_line: number;
  size: number;
  font: {
    family: string;
    sub_family: string;
  };
  color: string;
  stroke: {
    color: string;
    weight: number;
    opacity: number;
  } | null;
  shadow: {
    color: string;
    opacity: number;
    blur?: number;
    offset?: {
      x: number;
      y: number;
    };
  } | null;
  current_word_color: string | null;
  current_word_bg_color: string | null;
  amplified_word_color: string | null;
};

export type ISubtitles = {
  config: PresetConfig;
  items: ISubtitle[] | null;
  position: ISubtitlePosition;
};

export type ISubtitlePosition = {
  x: number;
  y: number;
};

export type ISubtitleWord = {
  start: number;
  end: number;
  word: string;
};

export type ISubtitle = {
  start: number;
  end: number;
  text: string;
  words: ISubtitleWord[];
};

export type IVideo = {
  captions?: ISubtitle[];
  config: IClipConfig;
  created_at: string;
  file_path: string;
  media_metadata: IVideoMetadata;
  thumbnail_path: string;
  title: string;
  updated_at: string;
  video_id: string;
  id: string;
};

export interface IVideoMetadata {
  duration_seconds: number;
  file_size_bytes: number;
  height: number;
  width: number;
  fps: number;
}

export interface IVideoSegment {
  start: number;
  end: number;
  crop_box: ICropBox | null;
  text?: string;
  transition?: TransitionConfig;
}

export interface ICropBox {
  x1: number;
  x2: number;
  y1: number;
  y2: number;
}

export type TransitionType = "fade" | "push" | "wipe" | "zoom" | "dissolve";
export type PushDirection = "left" | "right" | "up" | "down";
export type WipeDirection = "left" | "right" | "up" | "down" | "diagonal";

export interface TransitionConfig {
  type: TransitionType;
  durationFrames: number;
  options?: {
    pushDirection?: PushDirection;
    wipeDirection?: WipeDirection;
    zoomAmount?: number;
    pattern?: "random" | "diamonds" | "circles";
  };
}

export interface ProcessedSegment extends IVideoSegment {
  startFrame: number;
  endFrame: number;
  transitionDurationFrames: number;
}

export interface CanvasDimensions {
  // Target dimensions on the canvas
  width: number; // Width to render on canvas
  height: number; // Height to render on canvas

  // Position on the canvas (for centering)
  x: number; // X position to render on canvas
  y: number; // Y position to render on canvas

  // Source dimensions and position (from the video frame)
  sourceX: number; // Starting X position in source video (for cropping)
  sourceY: number; // Starting Y position in source video (for cropping)
  sourceWidth: number; // Width to take from source video
  sourceHeight: number; // Height to take from source video
}
