import { AbsoluteFill, staticFile, Audio, useCurrentFrame, useVideoConfig } from "remotion";
import { Subtitles } from "./components/Subtitles";
import { IClipConfig } from "./types";
import { ClipPreview } from "./ClipPreview";
import { useEffect, useState, useMemo } from "react";
import { loadFont, FontFormat } from "@remotion/fonts";
import { getCssFontFamily } from "../utils";

export const MyComposition: React.FC<{
  props: IClipConfig & {
    videoSrc: string;
    font: { family: string; subFamily: string; filePath: string };
  };
}> = ({ props }) => {
  const {
    sourceVideo,
    compositionDimensions,
    subtitles,
    segments,
    renderMode,
    videoSrc,
    font,
  } = props;

  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // Calculate audio startFrom using the same logic as ClipPreview
  const getAudioStartFrom = useMemo(() => {
    let summedUpDurations = 0;

    for (const section of segments) {
      summedUpDurations += section.end * fps - section.start * fps;

      if (summedUpDurations > frame) {
        return section.end * fps - summedUpDurations;
      }
    }

    return null;
  }, [frame, segments, fps]);

  const audioStartFrom = getAudioStartFrom;

  useEffect(() => {
    if (font && font.filePath) {
      // Extract font format from the URL (before query parameters)
      const urlWithoutQuery = font.filePath.split('?')[0];
      const fontExtension = urlWithoutQuery.split('.').pop()?.toLowerCase();

      let fontFormat: FontFormat;
      switch (fontExtension) {
        case 'otf':
          fontFormat = 'opentype';
          break;
        case 'ttf':
          fontFormat = 'truetype';
          break;
        case 'woff':
          fontFormat = 'woff';
          break;
        case 'woff2':
          fontFormat = 'woff2';
          break;
        default:
          fontFormat = 'opentype'; // Default fallback for .otf files
      }

      loadFont({
        family: getCssFontFamily(font.family, font.subFamily),
        url: font.filePath, // Use S3 URL directly
        format: fontFormat, // Explicitly specify the format
      });
    }
  }, [font?.family, font?.filePath, font?.subFamily]);

  return (
    <AbsoluteFill
      style={{
        position: "relative",
      }}
    >
      {audioStartFrom !== null && (
        <Audio
          src={videoSrc}
          startFrom={audioStartFrom}
        />
      )}
      <ClipPreview
        sourceVideoUrl={videoSrc}
        dimensions={compositionDimensions}
        sourceVideoMetadata={sourceVideo.media_metadata}
        isBuffering={false}
        cropMode={false}
        subtitles={subtitles}
        segments={segments}
      />
    </AbsoluteFill>
  );
};
