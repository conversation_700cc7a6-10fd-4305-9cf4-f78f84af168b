import React, { useMemo, useEffect, useRef, useState } from "react";
import {
  CalculateMetadataFunction,
  OffthreadVideo,
  useVideoConfig,
  useCurrentScale,
  useCurrentFrame,
  staticFile,
} from "remotion";
import { parseMedia } from "@remotion/media-parser";
import { ICropBox, ISubtitles, IVideoMetadata, IVideoSegment } from "./types";
import { Subtitles } from "./components/Subtitles";
import { detectAspectRatio, getFrameDimensions } from "../utils";

import Draggable, { DraggableData, DraggableEvent } from "react-draggable";
import { warn } from "console";

// Create a global cache to track which videos have been prefetched
// This prevents duplicate prefetch calls across multiple ClipPreview instances
const prefetchCache = new Set<string>();

// Constants for optimization
const PREMOUNT_FRAMES = 50; // Number of frames to premount segments
const OVERLAP_FRAMES = 3; // Number of frames to overlap between segments

type Props = {
  sourceVideoUrl: string;
  segments?: IVideoSegment[]; // now optional
  dimensions: { width: number; height: number };
  sourceVideoMetadata: IVideoMetadata;
  isBuffering: boolean;
  cropMode: boolean;
  subtitles?: ISubtitles; // now optional
  useImperativeSegmentIndex?: boolean; // new prop
};

type VideoToEmbed = {
  src: string;
  durationInFrames: number | null;
  startFrame: number;
};

export const calculateMetadata: CalculateMetadataFunction<any> = async ({
  props,
}) => {
  const segments = await Promise.all([
    ...props.segments.map(
      async (segment: IVideoSegment): Promise<VideoToEmbed> => {
        const { slowDurationInSeconds } = await parseMedia({
          src: props.sourceVideoUrl,
          fields: {
            slowDurationInSeconds: true,
          },
        });

        return {
          durationInFrames: Math.floor(
            slowDurationInSeconds * props.sourceVideoMetadata.fps
          ),
          startFrame: Math.floor(segment.start * props.sourceVideoMetadata.fps),
          src: props.sourceVideoUrl,
        };
      }
    ),
  ]);

  // Add a small buffer to the total duration to account for transitions
  const totalDurationInFrames =
    segments.reduce((acc, seg) => acc + (seg.durationInFrames ?? 0), 0) +
    OVERLAP_FRAMES;

  return {
    props: {
      ...props,
      segments,
    },
    durationInFrames: totalDurationInFrames,
  };
};

export const ClipPreview: React.FC<Props> = React.memo(
  ({
    sourceVideoUrl,
    segments: segmentsProp,
    dimensions,
    sourceVideoMetadata,
    isBuffering,
    cropMode = false,
    subtitles: subtitlesProp,
    useImperativeSegmentIndex = false,
  }) => {
    // Use props if provided, otherwise context
    const segments = segmentsProp;
    const subtitles = subtitlesProp;

    // Dev warning if neither is available
    if (!segments || !subtitles) {
      if (process.env.NODE_ENV === "development") {
        // eslint-disable-next-line no-console
        console.warn(
          "ClipPreview: Neither props nor context provided for segments/subtitles."
        );
      }
      return null;
    }

    // Get the current frame and fps
    const frame = useCurrentFrame();
    const { fps, durationInFrames } = useVideoConfig();
    const scale = useCurrentScale();

    // Calculate the current segment index
    let currentSegmentIndex: number | null = useMemo(() => {
      if (!segments || segments.length === 0) return 0;

      const currentTime = frame / fps;
      const firstStart = segments[0].start;
      const idx = segments.findIndex((segment) => {
        const relativeStart = segment.start - firstStart;
        const relativeEnd = segment.end - firstStart;

        return currentTime >= relativeStart && currentTime < relativeEnd;
      });
      return idx !== -1 ? idx : segments.length - 1;
    }, [frame, segments, fps]);

    const currentSegment: IVideoSegment | null = useMemo(
      () =>
        currentSegmentIndex !== null ? segments[currentSegmentIndex] : null,
      [segments, currentSegmentIndex]
    );

    const currentSegmentCropBox = useMemo(
      () => currentSegment?.crop_box || null,
      [currentSegment]
    );

    // Use the crop box for the current segment

    // Add a ref to the container div
    const containerRef = useRef<HTMLDivElement>(null);
    // Store element dimensions and position
    const [elementMetrics, setElementMetrics] = useState<{
      width: number;
      height: number;
      x: number;
      y: number;
      visual: { width: number; height: number };
      composition: { width: number; height: number };
    } | null>(null);

    const [currentFramePosition, setCurrentFramePosition] = useState<{
      x: number;
      y: number;
    }>({
      x: 0,
      y: 0,
    });

    const startFrom = useMemo(() => {
      let summedUpDurations = 0;

      for (const section of segments) {
        summedUpDurations += section.end * fps - section.start * fps;

        if (summedUpDurations > frame) {
          return section.end * fps - summedUpDurations;
        }
      }

      return null;
    }, [frame, segments]);

    if (startFrom === null) {
      return null;
    }

    // Measure the element dimensions and position
    useEffect(() => {
      if (!containerRef.current) return;

      // Get measurements
      const rect = {
        width: dimensions.width,
        height: dimensions.height,
        x: 0,
        y: 0,
      };

      // Calculate visual dimensions (what you see in DevTools)
      const visualWidth = rect.width * scale;
      const visualHeight = rect.height * scale;

      // Calculate composition dimensions (unscaled)
      const compositionWidth = rect.width / scale;
      const compositionHeight = rect.height / scale;

      // Store all metrics
      setElementMetrics({
        width: rect.width,
        height: rect.height,
        x: rect.x,
        y: rect.y,
        visual: {
          width: visualWidth,
          height: visualHeight,
        },
        composition: {
          width: compositionWidth,
          height: compositionHeight,
        },
      });

      // Use ResizeObserver to track size changes
      const resizeObserver = new ResizeObserver((entries) => {
        const { width, height } = entries[0].contentRect;
        const x = rect.x; // Use the initial x position
        const y = rect.y; // Use the initial y position

        setElementMetrics({
          width,
          height,
          x,
          y,
          visual: {
            width: width * scale,
            height: height * scale,
          },
          composition: {
            width: width / scale,
            height: height / scale,
          },
        });

        // set frame position here because in initiating crop mode the composition size is changing
        const ratio = width / sourceVideoMetadata.width;
        const currentSegment = segments[currentSegmentIndex ?? 0];

        if (currentSegment?.crop_box) {
          const currentFramePosition = {
            x: currentSegment.crop_box.x1 * ratio,
            y: currentSegment.crop_box.y1 * ratio,
          };

          setCurrentFramePosition(currentFramePosition);
        }
      });

      // Start observing
      resizeObserver.observe(containerRef.current);

      // Cleanup
      return () => resizeObserver.disconnect();
    }, [scale, currentSegmentIndex, segments, sourceVideoMetadata]);

    // Optimized prefetch with caching
    useEffect(() => {
      // Only prefetch if not already in cache
      if (sourceVideoUrl && !prefetchCache.has(sourceVideoUrl)) {
        // Add to cache before prefetching to prevent race conditions
        prefetchCache.add(sourceVideoUrl);

        // Use logLevel: 'warn' to track prefetching events if needed
        // prefetch(sourceVideoUrl, {
        //   logLevel: "warn",
        //   contentType: "video/mp4",
        // });
      }
    }, [sourceVideoUrl]);

    const handleFrameDrag = (
      e: DraggableEvent,
      position: DraggableData
    ): void => {
      setCurrentFramePosition({
        x: position.x,
        y: position.y,
      });
    };

    const handleFrameDragStop = (
      e: DraggableEvent,
      position: DraggableData
    ): void => {
      const currentSegment = segments[currentSegmentIndex ?? 0];

      if (currentSegment && currentSegment.crop_box) {
        const currentSegmentCropBox = currentSegment.crop_box;

        const ratio =
          (elementMetrics?.visual.width ?? 0) / sourceVideoMetadata.width;

        const scaledX1: number = (position.x * scale) / ratio;
        const scaledY1: number = (position.y * scale) / ratio;

        const newCropBox: ICropBox = {
          x1: +scaledX1.toFixed(2),
          x2: +(
            scaledX1 +
            (currentSegmentCropBox.x2 - currentSegmentCropBox.x1)
          ).toFixed(2),
          y1: +scaledY1.toFixed(2),
          y2: +(
            scaledY1 +
            (currentSegmentCropBox.y2 - currentSegmentCropBox.y1)
          ).toFixed(2),
        };
      }
    };

    const hasCropBox = segments.some((segment) => segment.crop_box !== null);

    const calculateDimensions = useMemo(() => {
      if (!hasCropBox) {
        const originalAspectRatio =
          sourceVideoMetadata.width / sourceVideoMetadata.height;
        const calculatedHeight = dimensions.width / originalAspectRatio;

        return {
          width: dimensions.width,
          height: calculatedHeight,
        };
      } else if (cropMode) {
        const cropBoxWidth = sourceVideoMetadata.width;
        const cropBoxHeight = sourceVideoMetadata.height;
        const cropBoxAspectRatio = cropBoxWidth / cropBoxHeight;
        const calculatedHeight = dimensions.width / cropBoxAspectRatio;

        return {
          width: dimensions.width,
          height: calculatedHeight,
        };
      } else if (currentSegmentCropBox) {
        const cropBoxWidth =
          currentSegmentCropBox.x2 - currentSegmentCropBox.x1;
        const cropBoxHeight =
          currentSegmentCropBox.y2 - currentSegmentCropBox.y1;
        const cropBoxAspectRatio = cropBoxWidth / cropBoxHeight;
        const calculatedHeight = dimensions.width / cropBoxAspectRatio;

        return {
          width: dimensions.width,
          height: calculatedHeight,
        };
      }

      return dimensions;
    }, [
      dimensions,
      hasCropBox,
      currentSegmentCropBox,
      sourceVideoMetadata,
      cropMode,
    ]);

    const calculateVideoStyles = () => {
      //   if (!currentSegmentCropBox) {
      //     return {
      //       height: calculateDimensions.height,
      //       width: "auto",
      //       position: "absolute",
      //       left: "50%",
      //       top: "50%",
      //       transform: "translate(-50%, -50%)",
      //     } as const;
      //   }

      let cropBoxToWorkWith = null;

      if (cropMode) {
        cropBoxToWorkWith = {
          x1: 0,
          x2: sourceVideoMetadata.width,
          y1: 0,
          y2: sourceVideoMetadata.height,
        };
      } else {
        cropBoxToWorkWith = currentSegmentCropBox;
      }

      const cropBoxWidth = cropBoxToWorkWith.x2 - cropBoxToWorkWith.x1;
      const cropBoxHeight = cropBoxToWorkWith.y2 - cropBoxToWorkWith.y1;
      const isPortrait = cropBoxHeight > cropBoxWidth;
      const widthScale = dimensions.width / cropBoxWidth;
      const heightScale = calculateDimensions.height / cropBoxHeight;
      const scale = Math.max(widthScale, heightScale);
      const scaledVideoWidth = sourceVideoMetadata.width * scale;
      const scaledVideoHeight = sourceVideoMetadata.height * scale;
      const left =
        -(cropBoxToWorkWith.x1 * scale) +
        (dimensions.width - cropBoxWidth * scale) / 2;
      const top =
        -(cropBoxToWorkWith.y1 * scale) +
        (calculateDimensions.height - cropBoxHeight * scale) / 2;

      return {
        position: "absolute",
        width: scaledVideoWidth,
        height: scaledVideoHeight,
        maxHeight: "100%",
        left,
        top,
        display: "block",
      } as const;
    };

    const cropBoxDimensions = useMemo(() => {
      if (!currentSegmentCropBox) {
        return null;
      }

      return {
        width: currentSegmentCropBox.x2 - currentSegmentCropBox.x1,
        height: currentSegmentCropBox.y2 - currentSegmentCropBox.y1,
      };
    }, [currentSegmentCropBox]);

    const frameDimensions = getFrameDimensions(
      detectAspectRatio(
        cropBoxDimensions?.width ?? 0,
        cropBoxDimensions?.height ?? 0
      ),
      {
        width: elementMetrics?.width ?? 0,
        height: elementMetrics?.height ?? 0,
      }
    );

    return (
      <>
        {cropMode && elementMetrics && (
          <div
            style={{
              position: "fixed",
              left: 0,
              top: 0,
              width: elementMetrics.width,
              height: elementMetrics.height,
              zIndex: 1000,
              overflow: "visible",
            }}
          >
            {/* Add dimming overlay around the frame */}
            <div
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
                // backgroundColor: "rgba(0, 0, 0, 0.5)",
                zIndex: 1,
              }}
            />

            <Draggable
              axis="both"
              position={currentFramePosition}
              bounds="parent"
              onDrag={handleFrameDrag}
              onStop={handleFrameDragStop}
              scale={scale}
            >
              <div
                className="video-frame"
                id="videoFrame"
                style={{
                  width: `${frameDimensions.frameWidth}px`,
                  height: `${frameDimensions.frameHeight}px`,
                  border: "1px solid #E1E1E1",
                  boxSizing: "border-box",
                  cursor: "move",
                  position: "relative",
                  overflow: "visible",
                  backgroundColor: "transparent",
                  zIndex: 2,
                  boxShadow: "0 0 0 9999px rgba(0, 0, 0, 0.5)",
                }}
              >
                {/* Corner handles - restyled to match Figma design */}
                <div
                  className="corner-handle top-left"
                  style={{
                    position: "absolute",
                    top: "-4px",
                    left: "-4px",
                    width: "8px",
                    height: "8px",
                    background: "#E1E1E1",
                    borderRadius: "50%",
                  }}
                ></div>

                <div
                  className="corner-handle top-right"
                  style={{
                    position: "absolute",
                    top: "-4px",
                    right: "-4px",
                    width: "8px",
                    height: "8px",
                    background: "#E1E1E1",
                    borderRadius: "50%",
                  }}
                ></div>

                <div
                  className="corner-handle bottom-left"
                  style={{
                    position: "absolute",
                    bottom: "-4px",
                    left: "-4px",
                    width: "8px",
                    height: "8px",
                    background: "#E1E1E1",
                    borderRadius: "50%",
                  }}
                ></div>

                <div
                  className="corner-handle bottom-right"
                  style={{
                    position: "absolute",
                    bottom: "-4px",
                    right: "-4px",
                    width: "8px",
                    height: "8px",
                    background: "#E1E1E1",
                    borderRadius: "50%",
                  }}
                ></div>

                {/* Grid lines - 3x3 grid as seen in Figma */}
                <div
                  style={{
                    position: "absolute",
                    top: "33.33%",
                    left: 0,
                    right: 0,
                    height: "1px",
                    background: "#E1E1E1",
                  }}
                ></div>

                <div
                  style={{
                    position: "absolute",
                    top: "66.66%",
                    left: 0,
                    right: 0,
                    height: "1px",
                    background: "#E1E1E1",
                  }}
                ></div>

                <div
                  style={{
                    position: "absolute",
                    left: "33.33%",
                    top: 0,
                    bottom: 0,
                    width: "1px",
                    background: "#E1E1E1",
                  }}
                ></div>

                <div
                  style={{
                    position: "absolute",
                    left: "66.66%",
                    top: 0,
                    bottom: 0,
                    width: "1px",
                    background: "#E1E1E1",
                  }}
                ></div>

                {/* Semi-transparent overlay for better visibility */}
                <div
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100%",
                    pointerEvents: "none",
                  }}
                ></div>
              </div>
            </Draggable>
          </div>
        )}
        <div
          ref={containerRef}
          style={{
            position: "relative",
            width: dimensions.width,
            height: dimensions.height,
            maxHeight: "100%",
            overflow: "hidden",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <OffthreadVideo
            pauseWhenBuffering
            startFrom={startFrom}
            src={sourceVideoUrl}
            muted={true}
            style={{
              ...calculateVideoStyles(),
            }}
          />

          {!isBuffering &&
            currentSegmentIndex !== null &&
            subtitles &&
            subtitles.items &&
            subtitles.items.length && (
              <Subtitles
                animationType="none"
                subtitles={subtitles}
                sourceVideoMetadata={sourceVideoMetadata}
                compositionDimensions={dimensions}
              />
            )}
        </div>
      </>
    );
  },
  // Custom equality function to prevent ANY rerenders during playback
  (prevProps, nextProps) => {
    // Only rerender if these props change, NOT on frame changes
    return (
      prevProps.sourceVideoUrl === nextProps.sourceVideoUrl &&
      prevProps.dimensions.width === nextProps.dimensions.width &&
      prevProps.dimensions.height === nextProps.dimensions.height &&
      prevProps.isBuffering === nextProps.isBuffering &&
      prevProps.segments === nextProps.segments &&
      prevProps.sourceVideoMetadata === nextProps.sourceVideoMetadata &&
      prevProps.cropMode === nextProps.cropMode &&
      prevProps.useImperativeSegmentIndex ===
      nextProps.useImperativeSegmentIndex
    );
  }
);
