apt update -y
apt install ffmpeg gconf-service libasound2 libatk1.0-0 libc6 libcairo2 libcups2 libdbus-1-3 libexpat1 libfontconfig1 libgcc1 libgconf-2-4 libgdk-pixbuf2.0-0 libglib2.0-0 libgtk-3-0 libnspr4 libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 libx11-6 libx11-xcb1 libxcb1 libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 libxtst6 ca-certificates fonts-liberation libnss3 lsb-release xdg-utils wget libgbm-dev -y


#  build-essential gawk bison -y

# wget https://ftp.gnu.org/gnu/bison/bison-3.2.tar.gz
# tar xf bison-3.2.tar.gz
# cd bison-3.2
# ./configure --prefix=$HOME/install
# make
# make install
# PATH=$HOME/install/bin:$PATH

# mkdir $HOME/glibc/ && cd $HOME/glibc
# wget http://ftp.gnu.org/gnu/libc/glibc-2.32.tar.gz
# tar -xvzf glibc-2.32.tar.gz
# mkdir build 
# mkdir glibc-2.32-install
# cd build
# ~/glibc/glibc-2.32/configure --prefix=$HOME/glibc/glibc-2.32-install
# make
# make install