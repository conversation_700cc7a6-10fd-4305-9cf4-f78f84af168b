#!/usr/bin/env ts-node

/**
 * Debug script for listing all available Remotion sites and their properties
 * 
 * This script:
 * 1. Loads environment variables from config.ts
 * 2. Retrieves all Remotion sites from AWS Lambda
 * 3. Displays detailed information about each site
 * 4. Shows composition metadata and input props schema
 * 
 * Usage:
 *   ts-node debug-remotion-sites.ts
 *   node debug-remotion-sites.js (if compiled)
 */

import { getSites, AwsRegion } from "@remotion/lambda/client";
import { bundle } from "@remotion/bundler";
import { getCompositions } from "@remotion/renderer";
import { config } from "./src/config";
import path from "path";

// ANSI color codes for better output formatting
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

function colorize(text: string, color: keyof typeof colors): string {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader(title: string): void {
  const border = '='.repeat(title.length + 4);
  console.log(colorize(border, 'cyan'));
  console.log(colorize(`  ${title}  `, 'cyan'));
  console.log(colorize(border, 'cyan'));
}

function printSubHeader(title: string): void {
  console.log(colorize(`\n--- ${title} ---`, 'yellow'));
}

function printProperty(key: string, value: any, indent: number = 0): void {
  const spaces = '  '.repeat(indent);
  if (typeof value === 'object' && value !== null) {
    console.log(`${spaces}${colorize(key + ':', 'blue')}`);
    if (Array.isArray(value)) {
      value.forEach((item, index) => {
        console.log(`${spaces}  [${index}]:`);
        if (typeof item === 'object') {
          Object.entries(item).forEach(([k, v]) => {
            printProperty(k, v, indent + 2);
          });
        } else {
          console.log(`${spaces}    ${item}`);
        }
      });
    } else {
      Object.entries(value).forEach(([k, v]) => {
        printProperty(k, v, indent + 1);
      });
    }
  } else {
    console.log(`${spaces}${colorize(key + ':', 'blue')} ${value}`);
  }
}

async function getLocalCompositions(): Promise<any[]> {
  try {
    printSubHeader("Bundling Local Remotion Project");
    
    const entryPoint = process.env.NODE_ENV === "production"
      ? path.resolve("./dist/remotion/index.js")
      : path.resolve("./src/remotion/index.ts");
    
    console.log(`Entry point: ${entryPoint}`);
    
    const bundleLocation = await bundle({
      entryPoint: entryPoint,
    });
    
    console.log(`Bundle location: ${bundleLocation}`);
    
    const compositions = await getCompositions(bundleLocation);
    return compositions;
  } catch (error) {
    console.error(colorize(`Error getting local compositions: ${error.message}`, 'red'));
    return [];
  }
}

async function main(): Promise<void> {
  try {
    printHeader("REMOTION SITES DEBUG INFORMATION");
    
    // Display configuration
    printSubHeader("Configuration");
    console.log(`AWS Region: ${config.lambda.region}`);
    console.log(`AWS Access Key ID: ${config.lambda.accessKeyId ? '***' + config.lambda.accessKeyId.slice(-4) : 'Not set'}`);
    console.log(`AWS Secret Access Key: ${config.lambda.secretAccessKey ? '***' + config.lambda.secretAccessKey.slice(-4) : 'Not set'}`);
    
    const region = config.lambda.region as AwsRegion;
    
    // Get deployed sites
    printSubHeader("Deployed Remotion Sites");
    console.log("Fetching sites from AWS Lambda...");
    
    const sites = await getSites({
      region: region,
    });
    
    if (sites.sites.length === 0) {
      console.log(colorize("❌ No deployed sites found!", 'red'));
      console.log("You may need to deploy a site first using:");
      console.log(colorize("npm run deploy-remotion-dev", 'yellow'));
    } else {
      console.log(colorize(`✅ Found ${sites.sites.length} deployed site(s)`, 'green'));
      
      sites.sites.forEach((site, index) => {
        console.log(colorize(`\n📦 Site ${index + 1}:`, 'magenta'));
        printProperty("Site Name", site.siteName, 1);
        printProperty("Serve URL", site.serveUrl, 1);
        printProperty("Bucket Name", site.bucketName, 1);
        printProperty("Created At", new Date(site.createdAt).toLocaleString(), 1);
        printProperty("Size in Bytes", site.sizeInBytes, 1);
        printProperty("Region", site.region, 1);
        
        if (site.stats) {
          printProperty("Stats", site.stats, 1);
        }
      });
    }
    
    // Get local compositions
    printSubHeader("Local Compositions");
    console.log("Analyzing local Remotion project...");
    
    const compositions = await getLocalCompositions();
    
    if (compositions.length === 0) {
      console.log(colorize("❌ No compositions found in local project!", 'red'));
    } else {
      console.log(colorize(`✅ Found ${compositions.length} composition(s)`, 'green'));
      
      compositions.forEach((comp, index) => {
        console.log(colorize(`\n🎬 Composition ${index + 1}:`, 'magenta'));
        printProperty("ID", comp.id, 1);
        printProperty("Width", comp.width, 1);
        printProperty("Height", comp.height, 1);
        printProperty("FPS", comp.fps, 1);
        printProperty("Duration (frames)", comp.durationInFrames, 1);
        printProperty("Duration (seconds)", (comp.durationInFrames / comp.fps).toFixed(2), 1);
        
        if (comp.defaultProps) {
          printProperty("Default Props", comp.defaultProps, 1);
        }
        
        if (comp.schema) {
          printProperty("Schema", comp.schema, 1);
        }
      });
    }
    
    // Summary
    printSubHeader("Summary");
    console.log(`• Deployed sites: ${colorize(sites.sites.length.toString(), 'green')}`);
    console.log(`• Local compositions: ${colorize(compositions.length.toString(), 'green')}`);
    console.log(`• AWS Region: ${colorize(region, 'blue')}`);
    
    if (sites.sites.length > 0) {
      console.log(`• Primary site URL: ${colorize(sites.sites[0].serveUrl, 'cyan')}`);
    }
    
    console.log(colorize("\n✨ Debug complete!", 'green'));
    
  } catch (error) {
    console.error(colorize(`\n❌ Error: ${error.message}`, 'red'));
    console.error(colorize(`Stack trace: ${error.stack}`, 'red'));
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error(colorize('Unhandled Rejection at:', 'red'), promise, colorize('reason:', 'red'), reason);
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main();
}
