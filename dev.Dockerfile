# Use an official Node.js runtime as a parent image
FROM node:20.9.0

COPY ./scripts /scripts
RUN chmod +x /scripts/*
RUN /scripts/setup-env.sh

# Set the working directory
WORKDIR /usr/src/app

# Install app dependencies
COPY package*.json ./
RUN npm install

# Install nodemon globally
RUN npm install -g nodemon

# Copy the rest of the application code
COPY . .

# Expose the port the app runs on
EXPOSE 8080

# Command to run the app using nodemon for hot-reloading
CMD ["nodemon", "--watch", ".", "--exec", "ts-node", "src/app.ts"]
